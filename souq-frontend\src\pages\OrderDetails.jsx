import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Package, Truck, MapPin, Clock, Star, Phone, Mail, RefreshCw } from 'lucide-react';
import { toast } from 'react-toastify';
import ShippingService from '../api/ShippingService';
import TransactionProgress from '../components/Transaction/TransactionProgress';
import TransactionManager from '../components/Transaction/TransactionManager';

const OrderDetails = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();
  const [order, setOrder] = useState(null);
  const [shipment, setShipment] = useState(null);
  const [loading, setLoading] = useState(true);
  const [trackingLoading, setTrackingLoading] = useState(false);

  // Base URL configuration for images
  const baseURL = import.meta.env.VITE_API_BASE_URL;
  const normalizedBaseURL = baseURL.endsWith('/') ? baseURL : `${baseURL}/`;

  useEffect(() => {
    loadOrderDetails();
  }, [orderId]);

  const loadOrderDetails = async () => {
    try {
      setLoading(true);
      const response = await ShippingService.getOrderDetails(orderId);
      
      if (response.success) {
        setOrder(response.data.order);
        setShipment(response.data.shipment);

        // Debug: Log order structure for transaction debugging
        console.log('🔍 OrderDetails - Order structure:', {
          _id: response.data.order?._id,
          orderNumber: response.data.order?.orderNumber,
          type: response.data.order?.type,
          status: response.data.order?.status,
          payment: response.data.order?.payment,
          product: response.data.order?.product,
          seller: response.data.order?.seller,
          productPhotos: response.data.order?.product?.product_photos
        });

        console.log('🎯 Transaction ID for API calls:', response.data.order?._id);
      }
    } catch (error) {
      console.error('Failed to load order details:', error);
      toast.error('Failed to load order details');
    } finally {
      setLoading(false);
    }
  };

  const refreshTracking = async () => {
    if (!order?.shipping?.trackingNumber) return;

    try {
      setTrackingLoading(true);
      const response = await ShippingService.trackShipment(order.shipping.trackingNumber);
      
      if (response.success) {
        setShipment(response.data.tracking);
        toast.success('Tracking information updated');
      }
    } catch (error) {
      console.error('Failed to refresh tracking:', error);
      toast.error('Failed to refresh tracking information');
    } finally {
      setTrackingLoading(false);
    }
  };

  const handleConfirmDelivery = async () => {
    try {
      const response = await ShippingService.confirmDelivery(orderId);
      if (response.success) {
        toast.success('Delivery confirmed');
        loadOrderDetails();
      }
    } catch (error) {
      console.error('Failed to confirm delivery:', error);
      toast.error(error.error || 'Failed to confirm delivery');
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'created':
      case 'pending_payment':
        return '📦';
      case 'paid':
      case 'processing':
        return '⚙️';
      case 'picked_up':
      case 'shipped':
        return '📤';
      case 'in_transit':
        return '🚛';
      case 'out_for_delivery':
        return '🚚';
      case 'delivered':
        return '✅';
      case 'cancelled':
        return '❌';
      default:
        return '📦';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600"></div>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Order Not Found</h2>
          <p className="text-gray-600 mb-4">The order you're looking for doesn't exist or you don't have access to it.</p>
          <button
            onClick={() => navigate('/orders')}
            className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
          >
            Back to Orders
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/orders')}
          className="p-2 hover:bg-gray-100 rounded-md transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900">Order #{order.orderNumber || 'N/A'}</h1>
          <p className="text-gray-600">Placed on {formatDate(order.createdAt)}</p>
        </div>
        <div className="flex items-center gap-2">
          <span className={`px-3 py-1 text-sm rounded-full ${ShippingService.getStatusColor(order.status)} bg-gray-100`}>
            {ShippingService.formatDeliveryStatus(order.status)}
          </span>
        </div>
      </div>

      {/* Transaction Progress */}
      {order && (
        <div className="mb-8">
          <TransactionProgress
            transaction={{
              transactionId: order._id, // Use the order._id which is actually the transaction/payment ID
              status: order.status,
              type: order.type
            }}
            transactionType={order.type || 'standard'}
            onRefresh={loadOrderDetails}
          />
        </div>
      )}

      {/* Transaction Management */}
      {order && (
        <div className="mb-8">
          <TransactionManager
            transactionId={order._id} // Use the order._id which is actually the transaction/payment ID
            userRole={order.buyer?._id === order.currentUserId ? 'buyer' : 'seller'}
            onStatusUpdate={(newStatus, data) => {
              console.log('Transaction status updated:', newStatus, data);
              // Refresh order details
              loadOrderDetails();
            }}
          />
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Product Information */}
          <div className="bg-white rounded-lg border shadow-sm p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Package className="w-5 h-5" />
              Product Details
            </h2>
            <div className="flex gap-4">
              <img
                src={order.product?.product_photos?.[0]
                  ? `${normalizedBaseURL}${order.product.product_photos[0]}`
                  : 'https://via.placeholder.com/96x96?text=No+Image'
                }
                alt={order.product?.title || 'Product'}
                className="w-24 h-24 object-cover rounded-md"
                onError={(e) => {
                  console.log('OrderDetails: Product image failed to load:', e.target.src);
                  e.target.src = 'https://via.placeholder.com/96x96?text=No+Image';
                }}
                onLoad={() => {
                  console.log('OrderDetails: Product image loaded successfully');
                }}
              />
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 mb-2">{order.product?.title || 'Product'}</h3>
                <div className="space-y-1 text-sm text-gray-600">
                  <p>Brand: {order.product?.brand || 'N/A'}</p>
                  <p>Size: {order.product?.size || 'N/A'}</p>
                  <p>Condition: {order.product?.condition || 'N/A'}</p>
                  {order.product?.material && <p>Material: {order.product.material}</p>}
                </div>
                <div className="mt-3">
                  <p className="text-lg font-semibold text-gray-900">
                    ${order.orderDetails?.offerAmount || order.orderDetails?.productPrice || '0.00'}
                  </p>
                  {order.orderDetails?.offerAmount && (
                    <p className="text-sm text-green-600">
                      Offer price (was ${order.orderDetails?.productPrice || '0.00'})
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Tracking Information */}
          {order.shipping?.trackingNumber && (
            <div className="bg-white rounded-lg border shadow-sm p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold flex items-center gap-2">
                  <Truck className="w-5 h-5" />
                  Tracking Information
                </h2>
                <button
                  onClick={refreshTracking}
                  disabled={trackingLoading}
                  className="flex items-center gap-2 px-3 py-1 text-sm bg-teal-600 text-white rounded-md hover:bg-teal-700 disabled:opacity-50"
                >
                  <RefreshCw className={`w-4 h-4 ${trackingLoading ? 'animate-spin' : ''}`} />
                  Refresh
                </button>
              </div>

              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                  <div>
                    <p className="font-medium">Tracking Number</p>
                    <p className="text-sm text-gray-600">{order.shipping.trackingNumber}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">Carrier</p>
                    <p className="text-sm text-gray-600">
                      {shipment?.provider?.displayName || 'Unknown'}
                    </p>
                  </div>
                </div>

                {shipment?.events && shipment.events.length > 0 && (
                  <div>
                    <h3 className="font-medium mb-3">Tracking History</h3>
                    <div className="space-y-3">
                      {shipment.events.map((event, index) => (
                        <div key={index} className="flex gap-3">
                          <div className="flex-shrink-0 w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center">
                            <span className="text-sm">{getStatusIcon(event.status)}</span>
                          </div>
                          <div className="flex-1">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium text-gray-900">{event.description}</p>
                                {event.location && (
                                  <p className="text-sm text-gray-600 flex items-center gap-1">
                                    <MapPin className="w-3 h-3" />
                                    {event.location.city}, {event.location.country}
                                  </p>
                                )}
                              </div>
                              <p className="text-sm text-gray-500">{formatDate(event.timestamp)}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {order.shipping?.estimatedDelivery && (
                  <div className="p-3 bg-blue-50 rounded-md">
                    <p className="font-medium text-blue-900 flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      Estimated Delivery
                    </p>
                    <p className="text-sm text-blue-700">
                      {formatDate(order.shipping.estimatedDelivery)}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Order Timeline */}
          <div className="bg-white rounded-lg border shadow-sm p-6">
            <h2 className="text-lg font-semibold mb-4">Order Timeline</h2>
            <div className="space-y-3">
              {order.timeline && order.timeline.length > 0 ? (
                order.timeline.map((event, index) => (
                  <div key={index} className="flex gap-3">
                    <div className="flex-shrink-0 w-3 h-3 bg-teal-600 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-gray-900">{event.description}</p>
                          <p className="text-sm text-gray-600">Updated by {event.updatedBy}</p>
                        </div>
                        <p className="text-sm text-gray-500">{formatDate(event.timestamp)}</p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4">
                  <p className="text-gray-500">No timeline events available</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Order Summary */}
          <div className="bg-white rounded-lg border shadow-sm p-6">
            <h2 className="text-lg font-semibold mb-4">Order Summary</h2>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span>Product Price</span>
                <span>${order.orderDetails?.productPrice || '0.00'}</span>
              </div>
              {order.orderDetails?.offerAmount && (
                <div className="flex justify-between text-green-600">
                  <span>Offer Discount</span>
                  <span>-${((order.orderDetails?.productPrice || 0) - (order.orderDetails?.offerAmount || 0)).toFixed(2)}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span>Platform Fee</span>
                <span>${order.payment?.fees?.platformFee || '0.00'}</span>
              </div>
              <div className="flex justify-between">
                <span>Shipping</span>
                <span>${order.payment?.fees?.shippingFee || '0.00'}</span>
              </div>
              <div className="flex justify-between">
                <span>Tax</span>
                <span>${order.payment?.fees?.tax || '0.00'}</span>
              </div>
              <hr />
              <div className="flex justify-between font-semibold">
                <span>Total</span>
                <span>${order.payment?.fees?.total || order.orderDetails?.offerAmount || order.orderDetails?.productPrice || '0.00'}</span>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-white rounded-lg border shadow-sm p-6">
            <h2 className="text-lg font-semibold mb-4">Contact Information</h2>
            <div className="space-y-4">
              <div>
                <p className="font-medium text-gray-900 mb-2">Seller</p>
                <div className="flex items-center gap-3">
                  <img
                    src={order.seller?.profile_picture
                    }
                    alt="Seller"
                    className="w-10 h-10 rounded-full"
                    onError={(e) => {
                      console.log('OrderDetails: Seller image failed to load:', e.target.src);
                      // e.target.src = 'https://via.placeholder.com/40x40?text=S';
                    }}
                    onLoad={() => {
                      console.log('OrderDetails: Seller image loaded successfully');
                    }}
                  />
                  <div>
                    <p className="font-medium">{order.seller?.username || 'Unknown Seller'}</p>
                    {order.seller?.email && (
                      <p className="text-sm text-gray-600 flex items-center gap-1">
                        <Mail className="w-3 h-3" />
                        {order.seller.email}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <p className="font-medium text-gray-900 mb-2">Shipping Address</p>
                <div className="text-sm text-gray-600">
                  <p>{order.shipping?.toAddress?.fullName || 'N/A'}</p>
                  <p>{order.shipping?.toAddress?.street1 || 'N/A'}</p>
                  {order.shipping?.toAddress?.street2 && (
                    <p>{order.shipping.toAddress.street2}</p>
                  )}
                  <p>
                    {order.shipping?.toAddress?.city || 'N/A'}, {order.shipping?.toAddress?.state || 'N/A'} {order.shipping?.toAddress?.zipCode || ''}
                  </p>
                  <p>{order.shipping?.toAddress?.country || 'N/A'}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          {['shipped', 'in_transit', 'out_for_delivery'].includes(order.status) && (
            <div className="bg-white rounded-lg border shadow-sm p-6">
              <h2 className="text-lg font-semibold mb-4">Actions</h2>
              <button
                onClick={handleConfirmDelivery}
                className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
              >
                Confirm Delivery
              </button>
              <p className="text-xs text-gray-500 mt-2">
                Click this button when you receive your order
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderDetails;
