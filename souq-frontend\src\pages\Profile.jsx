import { useEffect, useRef, useState } from "react";
import ProductGrid from "../components/Products/ProductGrid";
import ReviewSection from "../components/Profile/ReviewSection";
import RateProductModal from "../components/Profile/RateProductModal";
import {
    FaBoxOpen,
    FaMapMarkerAlt,
    FaClock,
    FaUserCheck,
    FaUserFriends,
    FaStar
} from "react-icons/fa";
import { FiMoreHorizontal } from "react-icons/fi"; // horizontal three-dot icon
import AuthModal from "../components/Auth/AuthModal";
import { useAppContext } from "../context/AppContext";
import LoginModal from "../components/Auth/LoginModal";
import ForgotPasswordModal from "../components/Auth/ForgotPasswordModal";
import SignUpModal from "../components/Auth/SignUpModal";
import { getProfileById } from "../api/AuthService";
import { useNavigate, useParams } from "react-router-dom";
import { EmptyState } from "../components/common/Animations";
import { CheckCircle2 } from "lucide-react";
import { formatDistanceToNowStrict } from 'date-fns';
import { follow, getUserProduct, unFollow } from "../api/ProductService";
import LoadingSpinner from "../components/common/LoadingSpinner";

export default function UserProfile() {
    const navigate = useNavigate()
    const { id } = useParams();
    const [activeTab, setActiveTab] = useState("listings");
    const [menuOpen, setMenuOpen] = useState(false);
    const [profile, setProfile] = useState(null);
    const [products, setProducts] = useState(null);
    const [loadingProfile, setLoadingProfile] = useState(false);
    const [loadingProducts, setLoadingProducts] = useState(false);
    const menuRef = useRef();
    const rating = 4;
    const {
        setIsAuthModalOpen,
        setAuthMode,
    } = useAppContext();

    const baseURL = import.meta.env.VITE_API_BASE_URL;
    const normalizedURL = baseURL.endsWith('/') ? baseURL.slice(0, -1) : baseURL;
    const [apiRefresh, setApiRefresh] = useState("");
    const [profileApiRefresh, setProfileApiRefresh] = useState("");
    const [following, setFollowing] = useState(profile?.isFollowingUser || false);
    const [showRateModal, setShowRateModal] = useState(false);
    const authUser = JSON.parse(localStorage.getItem("user"));

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (menuRef.current && !menuRef.current.contains(e.target)) {
                setMenuOpen(false);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    useEffect(() => {
        setLoadingProfile(true);
        getProfileById(id)
            .then((res) => {
                setProfile(res?.data?.data);
                setFollowing(res?.data?.data.isFollowingUser);
            })
            .catch((err) => {
                console.error("Failed to fetch profile", err);
            })
            .finally(() => {
                setLoadingProfile(false);
            });
    }, [id, profileApiRefresh]);

    useEffect(() => {
        if (profile?.id) {
            setLoadingProducts(true);
            getUserProduct(profile.id)
                .then((res) => {
                    const items = res?.data?.data?.items || [];
                    setProducts(items);
                })
                .catch((err) => {
                    console.log(err, "err");
                })
                .finally(() => {
                    setLoadingProducts(false);
                });
        }
    }, [profile?.id, apiRefresh]);

    const handleLogin = () => {
        setAuthMode('login');
        setIsAuthModalOpen(true);
    };

    // Single loader condition: show if either profile OR products loading
    // const isLoading = loadingProfile || loadingProducts || !profile;

    if (!profile) {
        return <LoadingSpinner fullScreen />;
    }


    const handleFollow = async () => {
        if (!authUser) {
            setAuthMode('login');
            setIsAuthModalOpen(true);
            return;
        }
        try {
            if (following) {
                await unFollow(profile.id).then((res) => {
                    if (res?.data?.success) {
                        setProfileApiRefresh(!profileApiRefresh)
                    }
                });
            } else {
                await follow(profile.id).then((res) => {
                    if (res?.data?.success) {
                        setProfileApiRefresh(!profileApiRefresh)
                    }
                })
            }
        } catch (error) {
            console.error('Error following/unfollowing:', error);
        }
    };

    const handleRateSeller = () => {
        if (!authUser) {
            setAuthMode('login');
            setIsAuthModalOpen(true);
            return;
        }
        setShowRateModal(true);
    };

    const handleRatingSubmitted = (ratingData) => {
        console.log('✅ Rating submitted for seller:', ratingData);
        // Optionally refresh the profile data to show updated ratings
        setProfileApiRefresh(!profileApiRefresh);
    };

    return (
        <div className="container mx-auto p-4">
            <div className="flex flex-col md:flex-row items-center md:items-start gap-8">
                <img
                    src={profile?.profile ? `${normalizedURL}${profile?.profile}` : "https://cdn-icons-png.flaticon.com/512/149/149071.png"}
                    alt="Profile"
                    className="w-44 h-44 rounded-full object-cover border-4 border-white shadow-md"
                />

                <div className="flex-1 space-y-3">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between w-full gap-2">
                        <h2 className="text-2xl font-semibold text-gray-800">{profile?.firstName} {profile?.lastName}</h2>
                        <div className="relative flex items-center gap-2">
                            <button className="bg-teal-600 text-white px-5 py-2 rounded-md hover:bg-teal-700" onClick={handleFollow}>
                                {following ? "Following" : "Follow"}
                            </button>
                            <button
                                className="bg-yellow-500 text-white px-5 py-2 rounded-md hover:bg-yellow-600 flex items-center gap-2"
                                onClick={handleRateSeller}
                            >
                                <FaStar className="w-4 h-4" />
                                Rate Seller
                            </button>
                            <div className="relative" ref={menuRef}>
                                <button
                                    className="text-gray-600 hover:text-black p-2"
                                    onClick={() => setMenuOpen(!menuOpen)}
                                >
                                    <FiMoreHorizontal size={20} />
                                </button>
                                {menuOpen && (
                                    <div className="absolute right-0 mt-2 w-32 bg-white border rounded-md shadow-lg z-10">
                                        <button className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100" onClick={handleLogin}>
                                            Report
                                        </button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Star Rating */}
                    <div className="flex items-center text-yellow-500 text-2xl">
                        {[...Array(5)].map((_, i) => (
                            <FaStar
                                key={i}
                                className={`text-xl ${i < rating ? 'text-yellow-500' : 'text-gray-300'}`}
                            />
                        ))}  <span className="text-gray-700 text-lg ml-2">{rating} reviews</span>
                    </div>

                    {/* Location, Last seen, Followers */}
                    <div className="flex items-start gap-12 flex-wrap text-md text-gray-600">
                        <div className="space-y-3">
                            <span className="text-sm font-semibold text-gray-700">About:</span>
                            <div className="flex items-center gap-2">
                                <FaMapMarkerAlt className="text-xl" />
                                <span>{profile?.city}, {profile?.country}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <FaClock className="text-xl" />
                                <span>Last seen {"  "}{profile?.lastLoginAt ? formatDistanceToNowStrict(new Date(profile?.lastLoginAt), { addSuffix: true }) : "1 Hours ago"}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <FaUserFriends className="text-xl" />
                                <span>
                                    <span className="font-semibold text-teal-600 underline cursor-pointer" onClick={() => navigate(`/followers/${profile.id}`)}>
                                        {profile?.followers}
                                    </span>{" "}
                                    followers,{" "}
                                    <span className="cursor-pointer font-semibold text-teal-600 underline" onClick={() => navigate(`/following/${profile.id}`)}>
                                        {profile?.following}
                                    </span>{" "}
                                    following
                                </span>
                            </div>
                        </div>

                        <div className="space-y-3">
                            <span className="text-sm font-semibold text-gray-700">Verified Info:</span>
                            <div className="flex items-center gap-2">
                                <CheckCircle2 />
                                <span>Email</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <CheckCircle2 />
                                <span>Google</span>
                            </div>
                        </div>
                    </div>
                    <span className="flex items-start flex-wrap text-md text-gray-600">{profile?.about}</span>
                </div>
            </div>

            <div className="mt-6 border-b flex gap-6 text-sm font-medium text-gray-600">
                <button
                    onClick={() => setActiveTab("listings")}
                    className={`pb-2 ${activeTab === "listings"
                        ? "border-b-2 border-teal-700 text-teal-700"
                        : "hover:text-teal-700"
                        }`}
                >
                    Listings
                </button>
                <button
                    onClick={() => setActiveTab("reviews")}
                    className={`pb-2 ${activeTab === "reviews"
                        ? "border-b-2 border-teal-700 text-teal-700"
                        : "hover:text-teal-700"
                        }`}
                >
                    Reviews
                </button>
            </div>

            {/* Tab Content */}
            <div className="mt-4 min-h-[600px]">
                {activeTab === "listings" ? (
                    <div className="flex-grow">
                        {loadingProducts ? (
                            <LoadingSpinner fullScreen={false} />
                        ) : products && products.length > 0 ? (
                            <ProductGrid products={products}
                                apiRefresh={apiRefresh}
                                setApiRefresh={setApiRefresh} />
                        ) : (
                            <EmptyState />
                        )}
                    </div>
                ) : (
                    <div className="text-gray-600">
                        <ReviewSection userProfile={profile} />
                    </div>
                )}
            </div>

            <AuthModal />
            <LoginModal />
            <ForgotPasswordModal />
            <SignUpModal />

            {/* Rate Product Modal */}
            <RateProductModal
                isOpen={showRateModal}
                onClose={() => setShowRateModal(false)}
                sellerProfile={profile}
                sellerProducts={products || []}
                onRatingSubmitted={handleRatingSubmitted}
            />
        </div>
    );
}
