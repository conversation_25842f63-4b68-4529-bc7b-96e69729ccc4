import { useEffect, useState } from "react";
import ProductGrid from "../components/Products/ProductGrid";
import { products } from "../data/products";
import ReviewSection from "../components/Profile/ReviewSection";
import {
    FaMapMarkerAlt,
    FaClock,
    FaUserCheck,
    FaUserFriends,
    FaStar,
    FaBoxOpen
} from "react-icons/fa";
import { LuPencil } from "react-icons/lu";
import AuthModal from "../components/Auth/AuthModal";
import LoginModal from "../components/Auth/LoginModal";
import ForgotPasswordModal from "../components/Auth/ForgotPasswordModal";
import SignUpModal from "../components/Auth/SignUpModal";
import { useAppContext } from "../context/AppContext";
import { useNavigate } from "react-router-dom";
import { getProfile } from "../api/AuthService";
import LoadingSpinner from "../components/common/LoadingSpinner";
import { EmptyState } from "../components/common/Animations";
import { getProduct } from "../api/ProductService";
import { CheckCircle2 } from "lucide-react";
import { formatDistanceToNowStrict } from 'date-fns';

export default function MemberProfile() {
    const [activeTab, setActiveTab] = useState("listings");
    const [profileData, setProfileData] = useState(null); // store user profile
    const baseURL = import.meta.env.VITE_API_BASE_URL.replace(/\/$/, '');
    const [isLoadingProducts, setIsLoadingProducts] = useState(true);

    const navigate = useNavigate();
    const rating = 4;
    const {
        setIsAuthModalOpen,
        setAuthMode,
    } = useAppContext();

    const handleLogin = () => {
        setAuthMode('login');
        setIsAuthModalOpen(true);
    };

    useEffect(() => {
        getProfile().then((res) => {
            console.log('👤 MemberProfile - getProfile response:', res);
            if (res?.success) {
                const userData = res?.data?.data;
                console.log('👤 MemberProfile - setting profileData:', userData);
                setProfileData(userData);
            }
        });
    }, []);

    const [product, setProduct] = useState([])
    const [user, setUser] = useState("")

    useEffect(() => {
        if (profileData?.id) {
            setIsLoadingProducts(true); // Start loading
            getProduct()
                .then((res) => {
                    const items = res?.data?.data?.items || [];
                    setProduct(items);
                    setUser(profileData);
                })
                .catch((err) => {
                    console.log(err, "err");
                })
                .finally(() => {
                    setIsLoadingProducts(false); // Stop loading
                });
        }
    }, [profileData?.id]);

    if (!profileData) {
        return <LoadingSpinner fullScreen />;
    }

    return (
        <div className="container mx-auto p-4">
            {/* Header */}
            <div className="flex flex-col md:flex-row items-center md:items-start gap-8">
                <img
                    src={profileData.profile ? `${baseURL}${profileData.profile}` : "https://cdn-icons-png.flaticon.com/512/149/149071.png"}
                    alt="Profile"
                    className="w-44 h-44 rounded-full object-cover border-4 border-white shadow-md"
                />

                <div className="flex-1 space-y-3">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between w-full gap-2">
                        <h2 className="text-2xl font-semibold text-gray-800">
                            {profileData?.firstName} {profileData?.lastName}
                        </h2>
                        <button
                            className="flex items-center gap-2 px-5 py-2 hover:bg-gray-100 text-teal-700 rounded-md font-semibold border border-teal-600"
                            onClick={() => navigate("/settings")}
                        >
                            <LuPencil className="w-4 h-4" />
                            Edit profile
                        </button>
                    </div>

                    {/* Star Rating */}
                    <div className="flex items-center text-yellow-500 text-2xl">
                        {[...Array(5)].map((_, i) => (
                            <FaStar
                                key={i}
                                className={`text-xl ${i < rating ? "text-yellow-500" : "text-gray-300"}`}
                            />
                        ))}
                        <span className="text-gray-700 text-lg ml-2">{rating} reviews</span>
                    </div>
                    {/* <div className="flex items-center gap-3 text-md text-gray-700">
                        <FaBoxOpen className="text-teal-700 text-2xl" />
                        <span className="text-teal-700 font-semibold">Frequent Uploads</span>
                        <span>· Regularly lists 5 or more items</span>
                    </div> */}

                    {/* Location, Last seen, Followers */}
                    <div className="flex items-start gap-12 flex-wrap text-md text-gray-600">
                        {/* Left Side - About Section */}
                        <div className="space-y-3">
                            <span className="text-sm font-semibold text-gray-700">About:</span>
                            <div className="flex items-center gap-2">
                                <FaMapMarkerAlt className="text-xl" />
                                <span>{profileData?.cityShow && `${profileData.city}, `}{profileData?.country}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <FaClock className="text-xl" />
                                <span>Last seen {"  "}{profileData?.lastLoginAt ? formatDistanceToNowStrict(new Date(profileData?.lastLoginAt), { addSuffix: true }) : "1 Hours ago"}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <FaUserFriends className="text-xl" />
                                <span>
                                    <span className="font-semibold text-teal-600 underline cursor-pointer" onClick={() => navigate(`/followers/${profileData.id}`)}>
                                        {profileData?.followers}
                                    </span>{" "}
                                    followers,{" "}
                                    <span className="cursor-pointer font-semibold text-teal-600 underline" onClick={() => navigate(`/following/${profileData.id}`)}>
                                        {profileData?.following}
                                    </span>{" "}
                                    following
                                </span>
                            </div>
                        </div>

                        {/* Right Side - Verified Info */}
                        <div className="space-y-3">
                            <span className="text-sm font-semibold text-gray-700">Verified Info:</span>
                            {profileData?.email && (
                                <div className="flex items-center gap-2">
                                    <CheckCircle2 />
                                    <span className=" ">Email</span>
                                </div>)}
                            {profileData?.loginWithGoogle && (<div className="flex items-center gap-2">
                                <CheckCircle2 />
                                <span className=" ">Google </span>
                            </div>)}
                            {profileData?.loginWithFacebook && (<div className="flex items-center gap-2">
                                <CheckCircle2 />
                                <span className=" ">Facebook </span>
                            </div>)}
                        </div>
                    </div>
                    <span className="flex items-start gap-12 flex-wrap text-md text-gray-600">{profileData?.about}</span>
                </div>
            </div>

            {/* Tabs */}
            <div className="mt-6 border-b flex gap-6 text-sm font-medium text-gray-600">
                {["listings", "reviews", "insights"].map((tab) => (
                    <button
                        key={tab}
                        onClick={() => setActiveTab(tab)}
                        className={`pb-2 ${activeTab === tab
                            ? "border-b-2 border-teal-700 text-teal-700"
                            : "hover:text-teal-700"
                            }`}
                    >
                        {tab.charAt(0).toUpperCase() + tab.slice(1)}
                    </button>
                ))}
            </div>

            {/* Tab Content */}
            <div className="mt-4 min-h-[600px]">
                {activeTab === "listings" ? (
                    <div className="flex-grow">
                        {isLoadingProducts ? (
                            <LoadingSpinner fullScreen={false} />
                        ) : product && product.length > 0 ? (
                            <ProductGrid products={product} user={user} />
                        ) : (
                            <EmptyState />
                        )}
                    </div>
                ) : activeTab === "reviews" ? (
                    <div className="text-gray-600">
                        {console.log('👤 MemberProfile - passing profileData to ReviewSection:', profileData)}
                        {profileData ? (
                            <ReviewSection userProfile={profileData} />
                        ) : (
                            <div className="flex justify-center items-center py-12">
                                <LoadingSpinner />
                            </div>
                        )}
                    </div>
                ) : (
                    <div className="text-gray-600">
                        Content for {activeTab} tab
                    </div>
                )}
            </div>

            <AuthModal />
            <LoginModal />
            <ForgotPasswordModal />
            <SignUpModal />
        </div>
    );
}
