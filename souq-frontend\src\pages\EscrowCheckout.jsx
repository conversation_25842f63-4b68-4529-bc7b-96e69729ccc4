import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Shield, ArrowLeft, CreditCard, Package, Truck, Globe, Edit3 } from 'lucide-react';
import PaymentGatewaySelector from '../components/Escrow/PaymentGatewaySelector';
import CurrencySelector from '../components/Currency/CurrencySelector';
import { createEscrowTransaction, initializeEscrowPayment } from '../api/EscrowService';
import { getProductDetails } from '../api/ProductService';
import { convertCurrency } from '../api/CurrencyService';
import { createStandardPayment, initializeStandardPayment, testStandardPaymentAPI } from '../api/StandardPaymentService';
import { getCardBrandInfo } from '../api/CardService';
import { getDefaultAddress } from '../api/AddressService';
import CountrySelector from '../components/Location/CountrySelector';
import CitySelector from '../components/Location/CitySelector';
import { searchCountries, searchCities } from '../api/LocationService';
import usePaymentSecurity from '../hooks/usePaymentSecurity';
// Card data extraction removed - handled on payment page

const EscrowCheckout = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Clear any previous payment status when starting new checkout
  const { startNewCheckout } = usePaymentSecurity();

  // Get data from navigation state
  const { product: passedProduct, productId, offerId, offerAmount, selectedCard, selectedBankAccount, paymentMethodType, shippingAddress: passedAddress, selectedShipping, paymentType } = location.state || {};

  // Remove card input states - will be handled on Stripe payment page

  // Debug logging
  console.log('EscrowCheckout - Navigation state:', location.state);
  console.log('EscrowCheckout - Product ID:', productId);
  console.log('EscrowCheckout - Passed Product:', passedProduct);
  console.log('EscrowCheckout - Payment Type:', paymentType);
  console.log('EscrowCheckout - Selected Shipping:', selectedShipping);
  console.log('EscrowCheckout - Product image structure:', {
    product_photos: passedProduct?.product_photos,
    photos: passedProduct?.photos,
    image: passedProduct?.image
  });
  
  const [product, setProduct] = useState(passedProduct || null);
  const [selectedGateway, setSelectedGateway] = useState(null);
  const [selectedCurrency, setSelectedCurrency] = useState('USD');
  const [convertedPrice, setConvertedPrice] = useState(null);
  const [exchangeRate, setExchangeRate] = useState(1);
  const [currencyLoading, setCurrencyLoading] = useState(false);
  const [shippingAddress, setShippingAddress] = useState(passedAddress || null);
  const [addressLoading, setAddressLoading] = useState(!passedAddress);
  const [isEditingAddress, setIsEditingAddress] = useState(false);
  const [gatewayFeePaidBy, setGatewayFeePaidBy] = useState('buyer');
  const [loading, setLoading] = useState(false);
  const [productLoading, setProductLoading] = useState(true);
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [selectedCity, setSelectedCity] = useState(null);
  const [agreementAccepted, setAgreementAccepted] = useState(false);

  // Clear previous payment status when starting new checkout
  useEffect(() => {
    startNewCheckout();
  }, [startNewCheckout]);

  // Load product details
  useEffect(() => {
    if (passedProduct) {
      // Product already passed, no need to fetch
      setProductLoading(false);
      return;
    }

    if (!productId) {
      toast.error('Product information missing');
      navigate('/');
      return;
    }

    loadProductDetails();

    // Load default address if not passed from previous page
    if (!passedAddress) {
      loadDefaultAddress();
    }
  }, [productId, passedProduct, passedAddress]);

  // Handle currency conversion
  useEffect(() => {
    try {
      console.log('🔄 Currency conversion useEffect triggered');
      console.log('Product:', product?.title);
      console.log('Selected currency:', selectedCurrency);
      console.log('Offer amount:', offerAmount);

      if (product && selectedCurrency && selectedCurrency !== 'USD') {
        console.log('🌍 Triggering currency conversion to', selectedCurrency);
        handleCurrencyConversion();
      } else if (product) {
        console.log('💵 Using USD pricing');
        setConvertedPrice(offerAmount || product.price);
        setExchangeRate(1);
      }
    } catch (error) {
      console.error('Error in currency conversion useEffect:', error);
      // Reset to safe defaults
      setSelectedCurrency('USD');
      setExchangeRate(1);
      setConvertedPrice(offerAmount || product?.price);
    }
  }, [selectedCurrency, product, offerAmount]);

  // Initialize address form with default values
  const initializeAddressForm = () => {
    if (!shippingAddress) {
      console.log('🏠 Initializing empty address form');
      setShippingAddress({
        fullName: '',
        street1: '',
        street2: '',
        city: '',
        state: '',
        zip: '',
        country: '',
        phoneNumber: '',
        addressType: 'home'
      });
      setSelectedCountry(null);
      setSelectedCity(null);
    } else {
      console.log('🏠 Address form already has data:', shippingAddress);
      // Don't reset selectors here - they will be populated by populateSelectorsFromAddress
    }
  };

  // Handle edit mode toggle
  const handleEditAddress = async () => {
    console.log('🏠 Entering edit mode, current address:', shippingAddress);
    initializeAddressForm();
    setIsEditingAddress(true);

    // Populate selectors when editing existing address
    if (shippingAddress) {
      await populateSelectorsFromAddress(shippingAddress);
    }
  };

  // Handle country selection
  const handleCountrySelect = (country) => {
    setSelectedCountry(country);
    setSelectedCity(null); // Clear city when country changes
    setShippingAddress(prev => ({
      ...prev,
      country: country ? country.name : '',
      city: '' // Clear city when country changes
    }));
  };

  // Handle city selection
  const handleCitySelect = (city) => {
    setSelectedCity(city);
    setShippingAddress(prev => ({
      ...prev,
      city: city ? city.name : '',
      state: city ? (city.state || '') : prev.state
    }));
  };

  // Function to populate selectors when editing existing address
  const populateSelectorsFromAddress = async (address) => {
    if (!address) return;

    console.log('🔍 Populating selectors from address:', address);

    // Find country by name
    if (address.country) {
      try {
        console.log('🔍 Searching for country:', address.country);
        const countryResponse = await searchCountries(address.country);
        if (countryResponse.success && countryResponse.data.countries?.length > 0) {
          const foundCountry = countryResponse.data.countries.find(
            c => c.name.toLowerCase() === address.country.toLowerCase()
          );
          if (foundCountry) {
            console.log('✅ Found country:', foundCountry);
            setSelectedCountry(foundCountry);

            // Find city by name within the found country
            if (address.city) {
              try {
                console.log('🔍 Searching for city:', address.city, 'in country:', foundCountry._id);
                const cityResponse = await searchCities(address.city, foundCountry._id);
                if (cityResponse.success && cityResponse.data.cities?.length > 0) {
                  const foundCity = cityResponse.data.cities.find(
                    c => c.name.toLowerCase() === address.city.toLowerCase()
                  );
                  if (foundCity) {
                    console.log('✅ Found city:', foundCity);
                    setSelectedCity(foundCity);
                  } else {
                    console.log('⚠️ City not found in API, keeping text value');
                    setSelectedCity(null);
                  }
                }
              } catch (error) {
                console.error('❌ Error searching for city:', error);
                setSelectedCity(null);
              }
            }
          } else {
            console.log('⚠️ Country not found in API, keeping text value');
            setSelectedCountry(null);
          }
        }
      } catch (error) {
        console.error('❌ Error searching for country:', error);
        setSelectedCountry(null);
      }
    }
  };

  // Save address changes
  const saveAddressChanges = async () => {
    try {
      if (!shippingAddress) {
        toast.error('Please fill in all required address fields');
        return;
      }

      // Validate required fields
      const requiredFields = ['fullName', 'street1', 'city', 'zipCode', 'country'];
      const missingFields = requiredFields.filter(field => !shippingAddress[field]?.trim());

      if (missingFields.length > 0) {
        toast.error(`Please fill in: ${missingFields.join(', ')}`);
        return;
      }

      console.log('💾 Saving address changes:', shippingAddress);

      // Here you could add API call to save the address if needed
      // For now, just close the edit mode
      setIsEditingAddress(false);
      toast.success('Address updated successfully');

    } catch (error) {
      console.error('❌ Failed to save address:', error);
      toast.error('Failed to save address changes');
    }
  };

  const loadDefaultAddress = async () => {
    try {
      setAddressLoading(true);
      console.log('🏠 Loading default address from API...');
      const response = await getDefaultAddress();
      console.log('🏠 Default address API response:', response);

      if (response.success && response.data.address) {
        const address = response.data.address;
        console.log('🏠 Raw address data received:', address);

        // Map all fields from the address model to match the form structure
        // Now using consistent field names: street1, street2
        const formattedAddress = {
          fullName: address.fullName || address.full_name || '',
          street1: address.street1 || '',
          street2: address.street2 || '',
          city: address.city || '',
          state: address.state || address.stateProvince || '',
          zip: address.zipCode || address.zip_code || address.zip || '',
          country: address.country || '',
          phoneNumber: address.phoneNumber || address.phone_number || address.phone || '',
          addressType: address.addressType || address.address_type || 'home'
        };

        console.log('🏠 Formatted address for display:', formattedAddress);
        console.log('🏠 Field mapping check:', {
          'address.fullName': address.fullName,
          'address.street1': address.street1,
          'address.city': address.city,
          'address.zipCode': address.zipCode,
          'formatted.fullName': formattedAddress.fullName,
          'formatted.street1': formattedAddress.street1
        });
        setShippingAddress(formattedAddress);

        // Populate selectors with country and city objects
        await populateSelectorsFromAddress(formattedAddress);
      } else {
        console.warn('🏠 No default address found or API returned failure');
        console.warn('🏠 Response details:', response);
        setShippingAddress(null);
      }
    } catch (error) {
      console.error('❌ Failed to load default address:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        stack: error.stack
      });

      // Set null instead of empty object to show "no address" state
      setShippingAddress(null);
    } finally {
      setAddressLoading(false);
    }
  };

  const loadProductDetails = async () => {
    try {
      setProductLoading(true);
      console.log('Loading product details for ID:', productId);
      const response = await getProductDetails(productId);
      console.log('Product API response:', response);

      if (response.success) {
        console.log('Product loaded successfully:', response.data.item);
        setProduct(response.data.item);
      } else {
        console.error('Failed to load product:', response);
        toast.error('Failed to load product details');
        navigate('/');
      }
    } catch (error) {
      console.error('Error loading product:', error);
      toast.error('Failed to load product details');
      navigate('/');
    } finally {
      setProductLoading(false);
    }
  };

  // Helper function to format currency amounts
  const formatCurrency = (amount, currency = selectedCurrency) => {
    return `${currency} ${(amount || 0).toFixed(2)}`;
  };

  const handleCurrencyChange = (newCurrency) => {
    try {
      console.log('🔄 Currency changing from', selectedCurrency, 'to', newCurrency);
      console.log('Current product price:', product?.price);
      console.log('Current offer amount:', offerAmount);
      setSelectedCurrency(newCurrency);
    } catch (error) {
      console.error('Error changing currency:', error);
      // Fallback to USD if there's an error
      setSelectedCurrency('USD');
      setExchangeRate(1);
      setConvertedPrice(offerAmount || product?.price);
    }
  };

  const handleCurrencyConversion = async () => {
    try {
      setCurrencyLoading(true);
      const basePrice = offerAmount || product.price;
      console.log('🔄 Converting', basePrice, 'USD to', selectedCurrency);

      const response = await convertCurrency({
        amount: basePrice,
        fromCurrency: 'USD',
        toCurrency: selectedCurrency
      });

      console.log('Currency conversion API response:', response);

      if (response.success && response.data && response.data.data) {
        const conversionData = response.data.data;
        console.log('Conversion data:', conversionData);

        setConvertedPrice(conversionData.convertedAmount || basePrice);
        setExchangeRate(conversionData.exchangeRate || 1);

        console.log('✅ Updated convertedPrice:', conversionData.convertedAmount);
        console.log('✅ Updated exchangeRate:', conversionData.exchangeRate);

        // Log fee conversions for debugging
        const newExchangeRate = conversionData.exchangeRate;
        console.log('💰 Fee conversions:');
        console.log('- Shipping USD:', (product.shipping_cost || 0), '→', selectedCurrency, ((product.shipping_cost || 0) * newExchangeRate).toFixed(2));
        console.log('- Sales Tax USD: 0.72 →', selectedCurrency, (0.72 * newExchangeRate).toFixed(2));
        console.log('- Platform Fee USD:', (finalPrice * (paymentType === 'standard' ? 0.05 : 0.1)), '→', selectedCurrency, ((finalPrice * (paymentType === 'standard' ? 0.05 : 0.1)) * newExchangeRate).toFixed(2));

        // Show success message with conversion details
        const totalFeesUSD = (product.shipping_cost || 0) + 0.72 + (finalPrice * (paymentType === 'standard' ? 0.05 : 0.1));
        const totalFeesConverted = totalFeesUSD * newExchangeRate;
        toast.success(`✅ Converted to ${selectedCurrency} | Fees: ${selectedCurrency} ${totalFeesConverted.toFixed(2)}`);
      } else {
        console.warn('❌ Currency conversion failed, falling back to USD');
        toast.error('Failed to convert currency');
        setConvertedPrice(basePrice);
        setExchangeRate(1);
        setSelectedCurrency('USD');
      }
    } catch (error) {
      console.error('❌ Currency conversion error:', error);
      toast.error('Failed to convert currency');
      setConvertedPrice(offerAmount || product.price);
      setExchangeRate(1);
      setSelectedCurrency('USD');
    } finally {
      setCurrencyLoading(false);
    }
  };

  const handleCreateEscrowTransaction = async () => {
    console.log('🚀 Pay with Escrow Protection button clicked!');
    console.log('Current state:', {
      selectedGateway: selectedGateway?.id,
      agreementAccepted,
      hasShippingAddress: !!shippingAddress,
      productId: productId || product?._id || product?.id
    });

    if (!selectedGateway) {
      console.log('❌ No payment gateway selected');
      toast.error('Please select a payment method');
      return;
    }

    if (!agreementAccepted) {
      console.log('❌ Escrow agreement not accepted');
      toast.error('Please accept the escrow agreement');
      return;
    }

    // Payment method validation removed - card details will be collected on payment page

    // Card validation removed - will be handled on Stripe payment page

    if (!shippingAddress) {
      console.log('❌ No shipping address provided');
      toast.error('Please add a shipping address');
      return;
    }

    console.log('Validation check - productId:', productId);
    console.log('Validation check - product:', product);
    console.log('Validation check - product._id:', product?._id);
    console.log('Validation check - product.id:', product?.id);
    console.log('Validation check - passedProduct:', passedProduct);

    const finalProductId = productId || product?._id || product?.id;
    if (!finalProductId) {
      console.error('Product validation failed - no product ID found');
      console.error('Navigation state was:', location.state);
      toast.error('Product information is missing. Redirecting back...');
      setTimeout(() => {
        navigate(-1); // Go back to previous page
      }, 2000);
      return;
    }

    try {
      setLoading(true);

      // Create escrow transaction
      const escrowData = {
        productId: finalProductId,
        offerId: offerId || null,
        paymentGateway: selectedGateway.id,
        shippingAddress,
        shippingCost: shippingCostUSD, // Send the selected shipping cost
        gatewayFeePaidBy,
        currency: selectedCurrency,
        cardDetails: selectedCard ? {
          cardId: selectedCard._id,
          cardBrand: selectedCard.cardBrand,
          lastFourDigits: selectedCard.lastFourDigits,
          cardholderName: selectedCard.cardholderName,
          expiryMonth: selectedCard.expiryMonth,
          expiryYear: selectedCard.expiryYear,
          isVerified: selectedCard.isVerified
        } : null,
        bankAccountDetails: selectedBankAccount ? {
          accountId: selectedBankAccount._id,
          accountHolderName: selectedBankAccount.accountHolderName,
          bankName: selectedBankAccount.bankName,
          accountType: selectedBankAccount.accountType,
          lastFourDigits: selectedBankAccount.lastFourDigits,
          routingNumber: selectedBankAccount.routingNumber,
          isVerified: selectedBankAccount.isVerified
        } : null,
        paymentMethodType: paymentMethodType || (selectedCard ? 'card' : 'bank')
      };

      console.log('Creating escrow transaction with data:', escrowData);
      console.log('Product ID:', productId);
      console.log('Selected Gateway:', selectedGateway);
      console.log('Shipping Address:', shippingAddress);
      console.log('Shipping Cost Details:', {
        selectedShipping,
        shippingCostUSD,
        productShippingCost: product?.shipping_cost
      });

      const escrowResponse = await createEscrowTransaction(escrowData);
      console.log('Escrow API response:', escrowResponse);

      if (!escrowResponse.success) {
        console.error('Escrow transaction creation failed:', escrowResponse);
        toast.error(escrowResponse.error || 'Failed to create escrow transaction');
        return;
      }

      console.log('✅ Escrow transaction created successfully');
      toast.success('Escrow transaction created successfully!');
      console.log('Full escrow response structure:', escrowResponse);
      console.log('Response data:', escrowResponse.data);

      // Handle the nested response structure from ApiService
      const responseData = escrowResponse.data?.data || escrowResponse.data;
      const escrowTransaction = responseData?.escrowTransaction || responseData;

      console.log('Response data after unwrapping:', responseData);
      console.log('Escrow transaction details:', escrowTransaction);

      if (!escrowTransaction || !escrowTransaction._id) {
        console.error('❌ Escrow transaction or ID is missing');
        console.error('Expected: escrowTransaction._id, Got:', escrowTransaction);
        console.error('Full response structure:', {
          escrowResponse,
          responseData,
          escrowTransaction
        });
        toast.error('Failed to get transaction details');
        return;
      }

      // Initialize payment
      const paymentData = {
        returnUrl: `${window.location.origin}/escrow/payment-success?transaction=${escrowTransaction._id}`,
        cancelUrl: `${window.location.origin}/escrow/payment-cancelled?transaction=${escrowTransaction._id}`
      };

      console.log('Initializing payment with data:', paymentData);
      console.log('Escrow transaction ID:', escrowTransaction._id);

      const paymentResponse = await initializeEscrowPayment(escrowTransaction._id, paymentData);
      console.log('🔍 Payment initialization response:', paymentResponse);
      console.log('🔍 Payment response data structure:', {
        success: paymentResponse.success,
        hasData: !!paymentResponse.data,
        hasClientSecret: !!paymentResponse.data?.clientSecret,
        hasPublishableKey: !!paymentResponse.data?.publishableKey,
        hasPaymentUrl: !!paymentResponse.data?.paymentUrl,
        hasTransactionId: !!paymentResponse.data?.transactionId,
        selectedGateway: selectedGateway?.id,
        dataKeys: paymentResponse.data ? Object.keys(paymentResponse.data) : []
      });

      if (paymentResponse.success) {
        console.log('✅ Payment initialization successful');
        toast.success('Payment initialization successful!');

        // Check if this is mock mode (for testing)
        // const isMockMode = paymentResponse.data.data.transactionId &&
        //   paymentResponse.data.data.transactionId.includes('mock');

        // if (isMockMode) {
        //   console.log('🎭 Mock mode detected - redirecting to success page');
        //   toast.success('Payment initialized successfully (Mock Mode)');
        //   navigate(`/escrow/payment-success?transaction=${escrowTransaction._id}`);
        //   return;
        // }

        // Redirect to payment gateway
        if (paymentResponse.data.data.paymentUrl) {
          console.log('Redirecting to payment URL:', paymentResponse.data.paymentUrl);
          window.location.href = paymentResponse.data.paymentUrl;
        } else if (selectedGateway?.id === 'stripe') {
          // For Stripe, we MUST have a client secret to proceed
          if (paymentResponse.data.data.clientSecret) {
            console.log('✅ Stripe client secret received, navigating to payment page');
            console.log('Client secret:', paymentResponse.data.data.clientSecret?.substring(0, 20) + '...');
            console.log('Publishable key:', paymentResponse.data.data.publishableKey?.substring(0, 20) + '...');
            console.log('Transaction ID:', escrowTransaction._id);

            console.log('🔄 Navigating to /escrow/stripe-payment...');
            navigate('/escrow/stripe-payment', {
              state: {
                clientSecret: paymentResponse.data.data?.clientSecret,
                publishableKey: paymentResponse.data.data?.publishableKey,
                transactionId: escrowTransaction._id,
                cardData: null, // No card data - will be collected on payment page
                autoProcess: false, // Disable auto-processing - user will enter card details
                paymentType: 'escrow'
              }
            });
            console.log('✅ Navigation command sent!');
          } else {
            // Stripe selected but no client secret - this is an error
            console.error('❌ Stripe selected but no client secret received');
            console.error('Payment response data:', paymentResponse.data);
            toast.error('Failed to initialize Stripe payment. Please try again.');
          }
        } else if (paymentResponse.data.data?.clientSecret) {
          // Other gateways with client secret
          console.log('✅ Client secret received for other gateway, navigating to payment page');
          navigate('/escrow/stripe-payment', {
            state: {
              clientSecret: paymentResponse.data.clientSecret,
              publishableKey: paymentResponse.data.publishableKey,
              transactionId: escrowTransaction._id,
              cardData: null,
              autoProcess: false,
              paymentType: 'escrow'
            }
          });
        } else {
          // Payment completed successfully, redirect to success page
          console.log('✅ Payment completed successfully, redirecting to success page');
          navigate(`/escrow/payment-success?transaction=${escrowTransaction._id}&type=escrow`);
        }
      } else {
        console.error('❌ Payment initialization failed:', paymentResponse);
        toast.error(paymentResponse.error || 'Failed to initialize payment');
      }

    } catch (error) {
      console.error('❌ Error in escrow payment process:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        response: error.response
      });

      // More specific error message based on the error
      if (error.response) {
        const status = error.response.status;
        const errorMessage = error.response.data?.error || error.response.data?.message || 'Unknown error';
        console.error(`API Error ${status}:`, errorMessage);
        toast.error(`Payment failed: ${errorMessage}`);
      } else if (error.message) {
        toast.error(`Payment failed: ${error.message}`);
      } else {
        toast.error('Failed to process payment');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCreateStandardPayment = async () => {
    if (!selectedGateway) {
      toast.error('Please select a payment method');
      return;
    }

    if (!shippingAddress) {
      toast.error('Please add a shipping address');
      return;
    }

    const finalProductId = productId || product?._id || product?.id;
    if (!finalProductId) {
      toast.error('Product information is missing');
      return;
    }

    try {
      setLoading(true);
      console.log('🔄 Creating standard payment...');

      // Create standard payment
      const paymentData = {
        productId: finalProductId,
        offerId: offerId || null,
        paymentGateway: selectedGateway.id,
        shippingAddress,
        gatewayFeePaidBy,
        currency: selectedCurrency,
        cardDetails: selectedCard ? {
          cardId: selectedCard._id,
          cardBrand: selectedCard.cardBrand,
          lastFourDigits: selectedCard.lastFourDigits
        } : null,
        bankAccountDetails: selectedBankAccount ? {
          accountId: selectedBankAccount._id,
          accountHolderName: selectedBankAccount.accountHolderName,
          bankName: selectedBankAccount.bankName,
          accountType: selectedBankAccount.accountType,
          lastFourDigits: selectedBankAccount.lastFourDigits,
          routingNumber: selectedBankAccount.routingNumber,
          isVerified: selectedBankAccount.isVerified
        } : null,
        paymentMethodType: paymentMethodType || (selectedCard ? 'card' : 'bank')
      };

      console.log('Creating standard payment with data:', paymentData);

      const paymentResponse = await createStandardPayment(paymentData);
      console.log('Standard payment API response:', paymentResponse);

      if (!paymentResponse.success) {
        console.error('❌ Standard payment creation failed:', paymentResponse);
        toast.error(paymentResponse.error || 'Failed to create payment');
        return;
      }

      console.log('✅ Standard payment created successfully');
      toast.success('Standard payment created successfully!');
      const paymentId = paymentResponse.data.paymentId;

      // Initialize payment with gateway
      const initData = {
        returnUrl: `${window.location.origin}/payment-success?transaction=${paymentId}&type=standard`,
        cancelUrl: `${window.location.origin}/payment-cancelled?transaction=${paymentId}&type=standard`
      };

      console.log('Initializing standard payment:', paymentId, initData);

      const initResponse = await initializeStandardPayment(paymentId, initData);
      console.log('Standard payment initialization response:', initResponse);

      if (initResponse.success) {
        console.log('✅ Standard payment initialization successful');
        toast.success('Standard payment initialization successful!');

        // Check if this is mock mode (for testing)
        const isMockMode = initResponse.data.transactionId &&
          initResponse.data.transactionId.includes('mock');

        if (isMockMode) {
          console.log('🎭 Mock mode detected - redirecting to success page');
          toast.success('Payment processed successfully');
          navigate(`/payment-success?transaction=${paymentId}&type=standard`);
          return;
        }

        // Redirect to payment gateway
        if (initResponse.data.paymentUrl) {
          console.log('Redirecting to payment URL:', initResponse.data.paymentUrl);
          window.location.href = initResponse.data.paymentUrl;
        } else if (initResponse.data.clientSecret) {
          // For Stripe, handle client-side payment
          console.log('Navigating to Stripe payment page');
          navigate('/stripe-payment', {
            state: {
              clientSecret: initResponse.data.clientSecret,
              publishableKey: initResponse.data.publishableKey,
              transactionId: paymentId,
              paymentType: 'standard'
            }
          });
        } else {
          // Payment completed successfully, redirect to success page
          console.log('✅ Standard payment completed successfully, redirecting to success page');
          navigate(`/payment-success?transaction=${paymentId}&type=standard`);
        }
      } else {
        console.error('❌ Standard payment initialization failed:', initResponse);
        toast.error(initResponse.error || 'Failed to initialize payment');
      }

    } catch (error) {
      console.error('❌ Error in standard payment process:', error);
      toast.error(error.error || error.message || 'Failed to process payment');
    } finally {
      setLoading(false);
    }
  };

  const handleTestStandardPaymentAPI = async () => {
    try {
      console.log('🧪 Testing standard payment API...');
      const result = await testStandardPaymentAPI();
      toast.success(`API test successful! User: ${result.data.user}`);
    } catch (error) {
      console.error('❌ API test failed:', error);
      toast.error(error.error || 'API test failed');
    }
  };

  if (productLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Product Not Found</h2>
          <p className="text-gray-600">Unable to load product information.</p>
        </div>
      </div>
    );
  }

  const finalPrice = offerAmount || product.price || 0;
  const displayPrice = convertedPrice || finalPrice;

  // Calculate fees in USD first, then convert to selected currency
  // Use selected shipping cost from checkout, fallback to product shipping cost
  const shippingCostUSD = selectedShipping?.cost?.total || product.shipping_cost || 0;
  const salesTaxUSD = 0.72; // Fixed sales tax amount in USD

  // Calculate fees based on payment type
  const platformFeeRate = paymentType === 'standard' ? 0.05 : 0.1; // 5% for standard, 10% for escrow
  const platformFeeUSD = finalPrice * platformFeeRate;

  // Convert fees to selected currency
  const currentExchangeRate = exchangeRate || 1;
  const shippingCost = selectedCurrency === 'USD' ? shippingCostUSD : (shippingCostUSD * currentExchangeRate);
  const salesTax = selectedCurrency === 'USD' ? salesTaxUSD : (salesTaxUSD * currentExchangeRate);
  const platformFee = selectedCurrency === 'USD' ? platformFeeUSD : (platformFeeUSD * currentExchangeRate);

  const baseAmountForProcessing = displayPrice + platformFee + shippingCost + salesTax;
  const processingFee = selectedGateway ? (baseAmountForProcessing * selectedGateway.feePercentage / 100) : 0;
  const baseURL = import.meta.env.VITE_API_BASE_URL;
  const normalizedBaseURL = baseURL.endsWith('/') ? baseURL : `${baseURL}/`;

  // Handle different image path structures
  const getProductImage = () => {
    // Try product_photos array first (from product API)
    if (product.product_photos && product.product_photos.length > 0) {
      const imagePath = product.product_photos[0];
      // Check if path already includes uploads/ prefix
      if (imagePath.startsWith('uploads/')) {
        return `${normalizedBaseURL}${imagePath}`;
      }
      return `${normalizedBaseURL}uploads/${imagePath}`;
    }
    // Try photos array (from chat API or other sources)
    if (product.photos && product.photos.length > 0) {
      const imagePath = product.photos[0];
      if (imagePath.startsWith('uploads/')) {
        return `${normalizedBaseURL}${imagePath}`;
      }
      return `${normalizedBaseURL}uploads/${imagePath}`;
    }
    // Try single image field
    if (product.image) {
      const imagePath = product.image;
      if (imagePath.startsWith('uploads/')) {
        return `${normalizedBaseURL}${imagePath}`;
      }
      return `${normalizedBaseURL}uploads/${imagePath}`;
    }
    // Fallback to placeholder
    return 'https://via.placeholder.com/80x80?text=No+Image';
  };

  const productImage = getProductImage();

  // Debug: Log the final product image URL
  console.log('🖼️ EscrowCheckout - Final product image URL:', productImage);
  console.log('🖼️ EscrowCheckout - Product data for image:', {
    product_photos: product?.product_photos,
    photos: product?.photos,
    image: product?.image
  });

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center text-teal-600 hover:text-teal-700 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </button>
          
          <div className="flex items-center space-x-3">
            {paymentType === 'standard' ? (
              <>
                <CreditCard className="w-8 h-8 text-blue-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Standard Payment Checkout</h1>
                  <p className="text-gray-600">Fast and secure payment processing</p>
                </div>
              </>
            ) : (
              <>
                <Shield className="w-8 h-8 text-teal-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Secure Escrow Checkout</h1>
                  <p className="text-gray-600">Your payment is protected until delivery confirmation</p>
                </div>
              </>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Product Summary */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>
              <div className="flex items-center space-x-4">
                <img
                  src={productImage}
                  alt={product.title}
                  className="w-20 h-20 object-cover rounded-lg"
                  onError={(e) => {
                    console.log('EscrowCheckout: Product image failed to load:', e.target.src);
                    e.target.src = 'https://via.placeholder.com/80x80?text=No+Image';
                  }}
                  onLoad={() => {
                    console.log('EscrowCheckout: Product image loaded successfully:', productImage);
                  }}
                />
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{product.title}</h3>
                  <p className="text-sm text-gray-600 mt-1">{product.description}</p>
                  <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                    <span>Brand: {product.brand}</span>
                    <span>Size: {product.size}</span>
                    <span>Condition: {product.condition}</span>
                  </div>
                </div>
                <div className="text-right">
                  {currencyLoading ? (
                    <div className="flex items-center justify-end space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-teal-600"></div>
                      <span className="text-sm text-gray-600">Converting...</span>
                    </div>
                  ) : (
                    <>
                      <div className="text-lg font-semibold text-gray-900">
                        {selectedCurrency} {(convertedPrice || finalPrice).toFixed(2)}
                      </div>
                      {offerAmount && (
                        <div className="text-sm text-green-600">
                          Offer Price Applied
                        </div>
                      )}
                      {selectedCurrency !== 'USD' && (
                        <div className="text-xs text-gray-500">
                          ≈ USD {(finalPrice || 0).toFixed(2)} (Rate: {(exchangeRate || 1).toFixed(4)})
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Currency Selection */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Currency & Pricing</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Globe className="w-4 h-4 inline mr-1" />
                    Payment Currency
                  </label>
                  <CurrencySelector
                    selectedCurrency={selectedCurrency}
                    onCurrencyChange={handleCurrencyChange}
                    showRates={true}
                    className="w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Price in {selectedCurrency}
                  </label>
                  <div className="px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
                    {currencyLoading ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-teal-600"></div>
                        <span className="text-sm text-gray-600">Converting to {selectedCurrency}...</span>
                      </div>
                    ) : (
                      <>
                        <div className="text-lg font-semibold text-gray-900">
                          {selectedCurrency} {(convertedPrice || finalPrice).toFixed(2)}
                        </div>
                        {selectedCurrency !== 'USD' && (
                          <div className="text-sm text-gray-500">
                            Original: USD {(finalPrice || 0).toFixed(2)} (Rate: 1 USD = {(exchangeRate || 1).toFixed(4)} {selectedCurrency})
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Shipping Address */}
            {/* <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Shipping Address</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name
                  </label>
                  <input
                    type="text"
                    value={shippingAddress.fullName}
                    onChange={(e) => setShippingAddress(prev => ({ ...prev, fullName: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    placeholder="Enter full name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    placeholder="Enter phone number"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Street Address
                  </label>
                  <input
                    type="text"
                    value={shippingAddress.street1}
                    onChange={(e) => setShippingAddress(prev => ({ ...prev, street1: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    placeholder="Enter street address"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    City
                  </label>
                  <input
                    type="text"
                    value={shippingAddress.city}
                    onChange={(e) => setShippingAddress(prev => ({ ...prev, city: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    placeholder="Enter city"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ZIP Code
                  </label>
                  <input
                    type="text"
                    value={shippingAddress.zip}
                    onChange={(e) => setShippingAddress(prev => ({ ...prev, zip: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    placeholder="Enter ZIP code"
                  />
                </div>
              </div>
            </div> */}


                {/* Shipping Address */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                  <Package className="w-5 h-5 mr-2" />
                  Shipping Address
                </h3>
                <button
                  onClick={handleEditAddress}
                  className="text-teal-600 hover:text-teal-700 p-2 rounded-lg hover:bg-teal-50 transition-colors"
                  title="Edit address"
                >
                  <Edit3 className="w-4 h-4" />
                </button>
              </div>

              {isEditingAddress ? (
                <div className="space-y-4">
                  {/* Debug: Show current address data */}
                  {/* {process.env.NODE_ENV === 'development' && (
                    <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                      <strong>Debug - Current Address Data:</strong>
                      <pre>{JSON.stringify(shippingAddress, null, 2)}</pre>
                    </div>
                  )} */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                      <input
                        type="text"
                        value={shippingAddress?.fullName || ''}
                        onChange={(e) => setShippingAddress(prev => ({...prev, fullName: e.target.value}))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                        placeholder="Enter your full name"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Country *</label>
                      <CountrySelector
                        selectedCountry={selectedCountry}
                        onCountrySelect={handleCountrySelect}
                        placeholder="Select Country"
                        required={true}
                        className="w-full"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Street Address *</label>
                    <input
                      type="text"
                      value={shippingAddress?.street1 || ''}
                      onChange={(e) => setShippingAddress(prev => ({...prev, street1: e.target.value}))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      placeholder="Enter your street address"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Apartment, suite, etc. (optional)</label>
                    <input
                      type="text"
                      value={shippingAddress?.street2 || ''}
                      onChange={(e) => setShippingAddress(prev => ({...prev, street2: e.target.value}))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      placeholder="Apartment, suite, unit, etc."
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">City *</label>
                      <CitySelector
                        selectedCountry={selectedCountry}
                        selectedCity={selectedCity}
                        onCitySelect={handleCitySelect}
                        placeholder="Select City"
                        required={true}
                        className="w-full"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">State/Province</label>
                      <input
                        type="text"
                        value={shippingAddress?.state || ''}
                        onChange={(e) => setShippingAddress(prev => ({...prev, state: e.target.value}))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                        placeholder="Enter state/province"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">ZIP Code *</label>
                      <input
                        type="text"
                        value={shippingAddress?.zipCode || ''}
                        onChange={(e) => setShippingAddress(prev => ({...prev, zipCode: e.target.value}))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                        placeholder="Enter ZIP code"
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number (optional)</label>
                    <input
                      type="tel"
                      value={shippingAddress?.phoneNumber || ''}
                      onChange={(e) => setShippingAddress(prev => ({...prev, phoneNumber: e.target.value}))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      placeholder="Enter phone number"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Address Type</label>
                    <select
                      value={shippingAddress?.addressType || 'home'}
                      onChange={(e) => setShippingAddress(prev => ({...prev, addressType: e.target.value}))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                    >
                      <option value="home">Home</option>
                      <option value="work">Work</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={() => setIsEditingAddress(false)}
                      className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={saveAddressChanges}
                      className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
                    >
                      Save Address
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-gray-700">
                  
                
                  {addressLoading ? (
                    <div className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-full mb-1"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3 mb-1"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  ) : shippingAddress ? (
                    <div className="space-y-1">
                      {/* Full Name with Address Type */}
                      <div className="flex items-center justify-between">
                        <p className="text-sm text-gray-900 font-semibold">{shippingAddress.fullName}</p>
                        {shippingAddress.addressType && (
                          <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full capitalize">
                            {shippingAddress.addressType}
                          </span>
                        )}
                      </div>

                      {/* Address Lines */}
                      <p className="text-sm text-gray-700">{shippingAddress.street1}</p>
                      {shippingAddress.street2 && (
                        <p className="text-sm text-gray-700">{shippingAddress.street2}</p>
                      )}

                      {/* City, State, ZIP */}
                      <p className="text-sm text-gray-700">
                        {shippingAddress.city}
                        {shippingAddress.state && `, ${shippingAddress.state}`}
                        {shippingAddress.zipCode && ` ${shippingAddress.zipCode}`}
                      </p>

                      {/* Country */}
                      <p className="text-sm text-gray-700">{shippingAddress.country}</p>

                      {/* Phone Number */}
                      {shippingAddress.phoneNumber && (
                        <p className="text-sm text-gray-600 flex items-center">
                          <span className="font-medium mr-1">Phone:</span>
                          {shippingAddress.phoneNumber}
                        </p>
                      )}
                    </div>
                  ) : (
                    <div className="text-gray-500">
                      <p>No shipping address available</p>
                      <button
                        onClick={handleEditAddress}
                        className="text-teal-600 hover:underline text-sm mt-1"
                      >
                        Add shipping address
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>


                    {/* Payment Gateway Selection */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <PaymentGatewaySelector
                amount={displayPrice + platformFee + shippingCost + salesTax}
                currency={selectedCurrency}
                onGatewaySelect={setSelectedGateway}
                selectedGateway={selectedGateway}
              />
            </div>

            {/* Payment Method Selection - Always show for Stripe escrow payments */}
            {(selectedGateway?.id === 'stripe' || (!selectedCard && !selectedBankAccount)) && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <CreditCard className="w-5 h-5 mr-2" />
                  Payment Method
                </h3>

                {selectedGateway?.id === 'stripe' && (
                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-sm text-blue-700">
                      <Shield className="w-4 h-4 inline mr-1" />
                      For secure escrow payments with Stripe, please enter your card details below.
                    </p>
                  </div>
                )}

                {/* Card details will be collected on the payment page */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <CreditCard className="w-5 h-5 text-blue-600" />
                    <div>
                      <h4 className="font-medium text-blue-900">Secure Payment</h4>
                      <p className="text-sm text-blue-700">
                        You'll enter your card details on the next secure payment page
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Selected Payment Method */}
            {(selectedCard || selectedBankAccount) && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <CreditCard className="w-5 h-5 mr-2" />
                  Payment Method
                </h3>
                <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {selectedCard ? (
                        <>
                          <span style={{ color: getCardBrandInfo(selectedCard.cardBrand).color }}>
                            {getCardBrandInfo(selectedCard.cardBrand).icon}
                          </span>
                          <div>
                            <p className="font-medium text-gray-900">
                              {getCardBrandInfo(selectedCard.cardBrand).name} •••• {selectedCard.lastFourDigits}
                            </p>
                            <p className="text-sm text-gray-600">
                              {selectedCard.cardholderName} • Expires {selectedCard.expiryMonth}/{selectedCard.expiryYear}
                            </p>
                          </div>
                        </>
                      ) : selectedBankAccount ? (
                        <>
                          <div className="text-2xl">🏦</div>
                          <div>
                            <p className="font-medium text-gray-900">****{selectedBankAccount.lastFourDigits}</p>
                            <p className="text-sm text-gray-600">
                              {selectedBankAccount.accountHolderName} • {selectedBankAccount.bankName || 'Bank Account'}
                            </p>
                            <p className="text-xs text-gray-500">
                              {selectedBankAccount.accountType.charAt(0).toUpperCase() + selectedBankAccount.accountType.slice(1)} Account
                            </p>
                          </div>
                        </>
                      ) : null}
                    </div>
                    {((selectedCard && selectedCard.isVerified) || (selectedBankAccount && selectedBankAccount.isVerified)) && (
                      <div className="flex items-center text-green-600">
                        <Shield className="w-4 h-4 mr-1" />
                        <span className="text-sm">Verified</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

          
      

            {/* Gateway Fee Options */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Processing Fee</h3>
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="gatewayFeePaidBy"
                    value="buyer"
                    checked={gatewayFeePaidBy === 'buyer'}
                    onChange={(e) => setGatewayFeePaidBy(e.target.value)}
                    className="text-teal-600 focus:ring-teal-500"
                  />
                  <span className="ml-3 text-sm text-gray-700">
                    I'll pay the processing fee (recommended)
                  </span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="gatewayFeePaidBy"
                    value="seller"
                    checked={gatewayFeePaidBy === 'seller'}
                    onChange={(e) => setGatewayFeePaidBy(e.target.value)}
                    className="text-teal-600 focus:ring-teal-500"
                  />
                  <span className="ml-3 text-sm text-gray-700">
                    Seller pays the processing fee
                  </span>
                </label>
              </div>
            </div>

            {/* Escrow Agreement */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Escrow Protection Agreement</h3>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <div className="flex items-start space-x-3">
                  <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div className="text-sm text-blue-800">
                    <h4 className="font-medium mb-2">How Escrow Protection Works:</h4>
                    <ul className="space-y-1 list-disc list-inside">
                      <li>Your payment is held securely until you confirm delivery</li>
                      <li>Seller receives payment only after successful transaction</li>
                      <li>10% platform fee is deducted from seller's payout</li>
                      <li>Automatic release after 7 days if no issues reported</li>
                      <li>Dispute resolution available if needed</li>
                    </ul>
                  </div>
                </div>
              </div>
              
              <label className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  checked={agreementAccepted}
                  onChange={(e) => setAgreementAccepted(e.target.checked)}
                  className="mt-1 text-teal-600 focus:ring-teal-500"
                />
                <span className="text-sm text-gray-700">
                  I agree to the escrow protection terms and understand that my payment 
                  will be held securely until I confirm delivery of the item.
                </span>
              </label>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Summary</h3>
              
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Product Price:</span>
                  {currencyLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-teal-600"></div>
                      <span className="text-xs text-gray-500">Converting...</span>
                    </div>
                  ) : (
                    <span className="font-medium">{selectedCurrency} {displayPrice.toFixed(2)}</span>
                  )}
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">
                    {paymentType === 'standard' ? 'Platform Fee:' : 'Escrow Protection Fee:'}
                  </span>
                  {currencyLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-teal-600"></div>
                      <span className="text-xs text-gray-500">Converting...</span>
                    </div>
                  ) : (
                    <span className="font-medium">{selectedCurrency} {platformFee.toFixed(2)}</span>
                  )}
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping:</span>
                  {currencyLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-teal-600"></div>
                      <span className="text-xs text-gray-500">Converting...</span>
                    </div>
                  ) : (
                    <span className="font-medium">{selectedCurrency} {shippingCost.toFixed(2)}</span>
                  )}
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">Sales tax:</span>
                  {currencyLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-teal-600"></div>
                      <span className="text-xs text-gray-500">Converting...</span>
                    </div>
                  ) : (
                    <span className="font-medium">{selectedCurrency} {salesTax.toFixed(2)}</span>
                  )}
                </div>

                {selectedGateway && gatewayFeePaidBy === 'buyer' && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Processing Fee:</span>
                    {currencyLoading ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-teal-600"></div>
                        <span className="text-xs text-gray-500">Converting...</span>
                      </div>
                    ) : (
                      <span className="font-medium">
                        {selectedCurrency} {processingFee.toFixed(2)}
                      </span>
                    )}
                  </div>
                )}

                {selectedCurrency !== 'USD' && !currencyLoading && (
                  <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded">
                    <div className="font-medium mb-1">Exchange Rate: 1 USD = {(exchangeRate || 1).toFixed(4)} {selectedCurrency}</div>
                    <div className="space-y-1">
                      <div>Original Product Price: USD {(finalPrice || 0).toFixed(2)}</div>
                      <div>Original Platform Fee: USD {((finalPrice || 0) * (paymentType === 'standard' ? 0.05 : 0.1)).toFixed(2)}</div>
                      <div>Original Shipping: USD {(product.shipping_cost || 0).toFixed(2)}</div>
                      <div>Original Sales Tax: USD 0.72</div>
                    </div>
                  </div>
                )}

                <div className="border-t border-gray-200 pt-3">
                  <div className="flex justify-between">
                    <span className="font-semibold text-gray-900">Total to Pay:</span>
                    {currencyLoading ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-teal-600"></div>
                        <span className="text-sm text-gray-600">Calculating total...</span>
                      </div>
                    ) : (
                      <span className="font-semibold text-gray-900">
                        {selectedCurrency} {(
                          displayPrice +
                          platformFee +
                          shippingCost +
                          salesTax +
                          (gatewayFeePaidBy === 'buyer' ? processingFee : 0)
                        ).toFixed(2)}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Debug Info */}
              {/* {process.env.NODE_ENV === 'development' && (
                <div className="mt-4 p-3 bg-gray-100 rounded-lg text-xs">
                  <strong>Debug Info:</strong>
                  <div>selectedGateway: {selectedGateway?.id || 'none'}</div>
                  <div>agreementAccepted: {agreementAccepted.toString()}</div>
                  <div>hasShippingAddress: {!!shippingAddress}</div>
                  <div>hasProductId: {!!(productId || product?._id || product?.id)}</div>
                </div>
              )} */}

              {/* Payment Buttons */}
              <div className="mt-6 space-y-3">
                {paymentType === 'standard' ? (
                  /* Standard Payment Button */
                  <button
                    onClick={handleCreateStandardPayment}
                    disabled={loading || productLoading || !selectedGateway || !shippingAddress || !(productId || product?._id || product?.id) || (!selectedCard && !selectedBankAccount)}
                    className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                  >
                    {loading ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Processing...
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <CreditCard className="w-4 h-4 mr-2" />
                        Pay with Standard Payment
                      </div>
                    )}
                  </button>
                ) : (
                  /* Escrow Payment Button */
                  <button
                    onClick={handleCreateEscrowTransaction}
                    disabled={loading || productLoading || !selectedGateway || !agreementAccepted || !shippingAddress || !(productId || product?._id || product?.id)}
                    className="w-full bg-teal-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-teal-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                  >
                    {loading ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Processing...
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <Shield className="w-4 h-4 mr-2" />
                        Pay with Escrow Protection
                      </div>
                    )}
                  </button>
                )}
              </div>

              {/* Debug Section */}
              {/* <div className="mt-4 p-3 bg-gray-100 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Debug Tools</h4>
                <button
                  onClick={handleTestStandardPaymentAPI}
                  className="w-full px-3 py-2 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 transition-colors"
                >
                  Test Standard Payment API
                </button>
              </div> */}

              <div className="mt-4 text-xs text-gray-500 text-center">
                🔒 Your payment information is encrypted and secure
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EscrowCheckout;
