# Stripe CLI Setup for Local Development

## Quick Setup Guide

### 1. Install Stripe CLI

#### Windows (Recommended)
```bash
# Using Chocolatey
choco install stripe-cli

# Or using Scoop
scoop install stripe

# Or download directly from:
# https://github.com/stripe/stripe-cli/releases/latest
```

#### macOS
```bash
# Using Homebrew
brew install stripe/stripe-cli/stripe
```

#### Linux
```bash
# Download and install
wget https://github.com/stripe/stripe-cli/releases/latest/download/stripe_linux_x86_64.tar.gz
tar -xvf stripe_linux_x86_64.tar.gz
sudo mv stripe /usr/local/bin
```

### 2. Login to Stripe
```bash
stripe login
```
This will open your browser to authenticate with your Stripe account.

### 3. Forward Webhooks to Local Development

#### For Escrow Payments
```bash
stripe listen --forward-to localhost:5000/api/user/escrow/webhook/stripe
```

#### For Standard Payments
```bash
stripe listen --forward-to localhost:5000/api/user/payments/webhook/stripe
```

#### For Both (Recommended)
Open two terminal windows and run both commands above.

### 4. Get Webhook Signing Secret
```bash
stripe listen --print-secret
```

Copy the webhook secret (starts with `whsec_`) and update your `.env` file:
```bash
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

### 5. Monitor Stripe Events

#### View All Events
```bash
stripe logs tail
```

#### Filter Specific Events
```bash
# Payment intents
stripe logs tail --filter-event-type payment_intent.succeeded

# All payment events
stripe logs tail --filter-event-type payment_intent.*

# Webhook events
stripe logs tail --filter-event-type payment_intent.succeeded,payment_intent.payment_failed
```

### 6. Test Payments

#### Test Card Numbers
```
Success: 4242 4242 4242 4242
Decline: 4000 0000 0000 0002
Insufficient Funds: 4000 0000 0000 9995
```

#### Test Payment Flow
1. Go to `http://localhost:5173`
2. Select a product
3. Choose "Buy Now" or "Make Offer"
4. Select Stripe as payment method
5. Use test card: `4242 4242 4242 4242`
6. Any future date for expiry
7. Any 3-digit CVC

### 7. Verify Integration

#### Check Stripe Dashboard
- Go to: https://dashboard.stripe.com/test/payments
- You should see payment intents and charges

#### Check Stripe CLI Output
You should see events like:
```
2024-07-09 11:24:58   --> payment_intent.created [pi_xxx]
2024-07-09 11:25:15   --> payment_intent.succeeded [pi_xxx]
2024-07-09 11:25:15  <--  [200] POST http://localhost:5000/api/user/payments/webhook/stripe
```

#### Check Backend Logs
Look for:
```
📥 Standard payment webhook received from stripe
✅ Standard payment webhook processed successfully
💰 Payment completed and wallet credited
```

## Troubleshooting

### Webhook Not Received
1. **Check CLI is running**: Ensure `stripe listen` is active
2. **Verify URL**: Confirm webhook URL matches your backend route
3. **Check firewall**: Ensure port 5000 is accessible
4. **Restart CLI**: Stop and restart `stripe listen`

### Payment Not Appearing in Dashboard
1. **Check API keys**: Ensure using correct test keys
2. **Verify account**: Confirm you're logged into correct Stripe account
3. **Check test mode**: Ensure dashboard is in test mode

### Frontend Errors
1. **Check publishable key**: Verify frontend receives correct key
2. **Browser console**: Look for Stripe loading errors
3. **Network tab**: Check for failed API calls

## Expected Workflow

### Successful Payment Flow
1. **Frontend**: User enters payment details
2. **Stripe**: Creates payment intent
3. **Frontend**: Confirms payment with Stripe
4. **Stripe**: Processes payment and sends webhook
5. **Backend**: Receives webhook and updates database
6. **Dashboard**: Shows completed payment
7. **CLI**: Displays webhook events

### What You Should See

#### Stripe Dashboard
- Payment intents in "Payments" section
- Successful charges with amounts
- Customer information
- Payment method details

#### Stripe CLI
- Real-time webhook events
- HTTP status codes (200 = success)
- Event types and IDs

#### Backend Console
- Payment initialization logs
- Webhook processing logs
- Database update confirmations

## Commands Reference

```bash
# Login
stripe login

# Listen for webhooks
stripe listen --forward-to localhost:5000/api/user/payments/webhook/stripe

# Get webhook secret
stripe listen --print-secret

# Monitor events
stripe logs tail

# Test webhook endpoint
stripe trigger payment_intent.succeeded

# Check account info
stripe config --list
```

## Next Steps

1. **Install Stripe CLI** using one of the methods above
2. **Run webhook forwarding** for your payment type
3. **Test a payment** through the frontend
4. **Monitor the CLI output** to see webhook events
5. **Check Stripe dashboard** to confirm transactions appear

After completing these steps, all Stripe payments should be visible in both the Stripe dashboard and CLI!
