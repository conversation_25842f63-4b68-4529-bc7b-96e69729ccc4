import { useState, useEffect } from 'react';
import {
  Wallet as WalletIcon,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Download,
  Settings,
  Package,
  Eye,
  EyeOff,
  RefreshCw
} from 'lucide-react';
import { toast } from 'react-toastify';
import {
  getWallet,
  getTransactionHistory,
  getWalletStatistics,
  withdrawMoney,
  updateWalletSettings,
  completePayment,
  getSellerOrders,
  formatCurrency,
  formatTransactionType,
  getTransactionIcon,
  getTransactionColor
} from '../../api/WalletService';

// Helper function for date formatting
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
};
import RatingPrompt from '../Rating/RatingPrompt';

const Wallet = () => {
  const [wallet, setWallet] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [statistics, setStatistics] = useState(null);
  const [orders, setOrders] = useState([]);
  const [totalOrdersAmount, setTotalOrdersAmount] = useState(0);
  const [ordersPagination, setOrdersPagination] = useState(null);
  const [ordersLoading, setOrdersLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showBalance, setShowBalance] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [selectedOrderForRating, setSelectedOrderForRating] = useState(null);
  const [withdrawalData, setWithdrawalData] = useState({
    amount: '',
    currency: 'AED', // Changed default to AED
    withdrawalMethod: 'bank_transfer',
    description: ''
  });
  const [selectedCurrency, setSelectedCurrency] = useState('AED'); // Add currency selector state
console.log('Wallet component rendered',orders);

  useEffect(() => {
    loadWalletData();

    // Set up auto-refresh every 30 seconds
    const interval = setInterval(() => {
      loadWalletData();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  // Update selected currency when wallet data loads
  useEffect(() => {
    if (wallet?.primaryCurrency) {
      setSelectedCurrency(wallet.primaryCurrency);
    }
  }, [wallet]);

  const loadWalletData = async () => {
    try {
      setLoading(true);

      // Load wallet details
      const walletResponse = await getWallet();

      if (walletResponse.success) {
        setWallet(walletResponse.data?.data?.wallet);
      } else {
        console.error('❌ Failed to load wallet:', walletResponse.message);
        toast.error(walletResponse.message || 'Failed to load wallet data');
      }

      // Load transaction history
      console.log('📋 Fetching transaction history...');
      const transactionsResponse = await getTransactionHistory({ limit: 10 });
      console.log('📋 Transactions response:', transactionsResponse);

      if (transactionsResponse.success && transactionsResponse.data) {
        const transactions = transactionsResponse.data.transactions || transactionsResponse.data || [];
        setTransactions(transactions);
        console.log('✅ Transactions loaded:', transactions);
        console.log('📊 Transaction count:', transactions.length);
        if (transactions.length > 0) {
          console.log('📊 First transaction:', transactions[0]);
        }
      } else {
        console.warn('⚠️ Failed to load transactions:', transactionsResponse.message || 'No data');
        setTransactions([]); // Set empty array as fallback
      }

      // Load statistics
      console.log('📈 Fetching wallet statistics...');
      const statsResponse = await getWalletStatistics();
      console.log('📈 Statistics response:', statsResponse);

      if (statsResponse.success && statsResponse.data) {
        setStatistics(statsResponse.data);
        console.log('✅ Statistics loaded:', statsResponse.data);
        console.log('📊 Period stats:', statsResponse.data.period);
        console.log('📊 Overall stats:', statsResponse.data.overall);
      } else {
        console.warn('⚠️ Failed to load statistics:', statsResponse.message || 'No data');
        // Set default statistics structure
        setStatistics({
          period: { totalEarned: 0, totalSpent: 0, totalTransactions: 0 },
          overall: { totalWithdrawn: 0, totalEarned: 0, totalTransactions: 0 }
        });
      }

      // Load seller orders
      await loadOrdersData();

    } catch (error) {
      console.error('❌ Error loading wallet data:', error);
      toast.error('Failed to load wallet data: ' + (error.message || 'Unknown error'));
    } finally {
      setLoading(false);
      console.log('✅ Wallet data loading completed');
    }
  };

  const loadOrdersData = async () => {
    try {
      setOrdersLoading(true);
      console.log('📦 Loading seller orders...');
      const ordersResponse = await getSellerOrders(1, 100); // Get more orders to calculate total

      console.log('📦 Orders API Response:', ordersResponse);

      if (ordersResponse.success) {
        const ordersData = ordersResponse.data.data?.orders || [];
        const pagination = ordersResponse.data.pagination || null;

        console.log('📦 Orders Data:', ordersData);
        console.log('📦 Pagination:', pagination);

        setOrders(ordersData);
        setOrdersPagination(pagination);

        // Calculate total amount from all orders
        const total = ordersData.reduce((sum, order) => {
          // Use the correct field from API response
          const amount = order.orderDetails?.productPrice || order.payment?.fees?.total || 0;
          console.log(`📦 Order ${order.orderNumber}: $${amount}`);
          return sum + parseFloat(amount);
        }, 0);

        setTotalOrdersAmount(total);

        console.log(`📦 Loaded ${ordersData.length} orders, total amount: $${total}`);
        console.log('📦 Orders state after setting:', ordersData);
        toast.success(`Loaded ${ordersData.length} orders, total: $${total.toFixed(2)}`);
      } else {
        console.error('❌ Failed to load orders:', ordersResponse.message);
        console.error('❌ Full response:', ordersResponse);
        toast.error('Failed to load orders: ' + ordersResponse.message);
      }
    } catch (error) {
      console.error('❌ Error loading orders:', error);
      toast.error('Error loading orders: ' + error.message);
    } finally {
      setOrdersLoading(false);
    }
  };

  const handleCurrencyChange = async (newCurrency) => {
    try {
      setLoading(true);
      const response = await updateWalletSettings({
        primaryCurrency: newCurrency
      });

      if (response.success) {
        setSelectedCurrency(newCurrency);
        toast.success(`Primary currency updated to ${newCurrency}`);
        loadWalletData(); // Reload wallet data to reflect changes
      } else {
        toast.error(response.message || 'Failed to update currency');
      }
    } catch (error) {
      console.error('Currency update error:', error);
      toast.error(error.message || 'Failed to update currency');
    } finally {
      setLoading(false);
    }
  };

  const handleWithdraw = async (e) => {
    e.preventDefault();

    try {
      const response = await withdrawMoney(withdrawalData);

      if (response.success) {
        toast.success('Withdrawal request submitted successfully');
        setShowWithdrawModal(false);
        setWithdrawalData({
          amount: '',
          currency: selectedCurrency, // Use selected currency instead of hardcoded USD
          withdrawalMethod: 'bank_transfer',
          description: ''
        });
        loadWalletData(); // Refresh data
      } else {
        toast.error(response.message || 'Failed to process withdrawal');
      }
    } catch (error) {
      console.error('Withdrawal error:', error);
      toast.error('Failed to process withdrawal');
    }
  };









  const getOrderStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'pending': return 'text-yellow-600';
      case 'cancelled': return 'text-red-600';
      case 'processing': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getPaymentMethodColor = (method) => {
    switch (method) {
      case 'escrow': return 'bg-purple-100 text-purple-700';
      case 'standard': return 'bg-blue-100 text-blue-700';
      case 'stripe': return 'bg-green-100 text-green-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };





  const handleCompleteOrderPayment = async (order) => {
    try {
      console.log('💰 Completing order payment:', order);
      console.log('💰 Order details:', {
        transactionId: order.payment?.transactionId,
        transactionType: order.type,
        orderNumber: order.orderNumber,
        orderId: order._id,
        fullOrder: order
      });

      // For escrow orders, we need to use the order ID to find the escrow transaction
      let transactionId = order.payment?.transactionId;

      // If it's an escrow order, try multiple approaches to find the transaction
      if (order.type === 'escrow') {
        console.log('🛡️ Escrow order detected, trying multiple transaction ID approaches:');
        console.log(`🛡️   - Order payment.transactionId: ${order.payment?.transactionId}`);
        console.log(`🛡️   - Order number: ${order.orderNumber}`);
        console.log(`🛡️   - Order _id: ${order._id}`);

        // Try the order ID first (most likely to work for escrow)
        transactionId = order._id;
        console.log(`🛡️   - Using order._id as transaction ID: ${transactionId}`);
      }

      // Validate transaction ID exists
      if (!transactionId) {
        toast.error(`Order ${order.orderNumber} has no transaction ID`);
        return;
      }

      const paymentData = {
        transactionId: transactionId,
        transactionType: order.type // Let backend auto-detect the type
      };

      console.log('💰 Sending payment completion request:', paymentData);

      const response = await completePayment(paymentData);

      console.log('💰 Payment completion response:', response);

      if (response.success) {
        if (response.data.alreadyCompleted) {
          toast.info(`Order ${order.orderNumber} payment was already completed`);
        } else if (response.data.walletCredited) {
          toast.success(`Order ${order.orderNumber} payment completed! Wallet credited: ${response.data.currency} ${response.data.sellerAmount}`);
        } else {
          toast.info(`Order ${order.orderNumber} payment processed (no wallet credit needed)`);
        }

        // Mark this order as payment completed to hide the button
        setOrders(prevOrders =>
          prevOrders.map(o =>
            o._id === order._id
              ? { ...o, paymentCompleted: true }
              : o
          )
        );

        // Refresh wallet and orders data
        await loadWalletData();
        await loadOrdersData();
      } else {
        const errorMessage = response.message || response.error || 'Unknown error occurred';
        console.error('💰 Payment completion failed:', errorMessage);
        toast.error(`Failed to complete order ${order.orderNumber}: ${errorMessage}`);
      }
    } catch (error) {
      console.error('Complete order payment error:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });

      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.message ||
                          'Unknown error occurred';

      toast.error(`Failed to complete order ${order.orderNumber}: ${errorMessage}`);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading wallet data...</p>
        </div>
      </div>
    );
  }

  // Show error state if wallet failed to load
  if (!wallet) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="text-center py-12">
          <WalletIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Wallet Not Available</h3>
          <p className="text-gray-600 mb-4">
            We're having trouble loading your wallet. Please try again.
          </p>
          <button
            onClick={loadWalletData}
            className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <WalletIcon className="w-8 h-8 text-teal-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Wallet</h1>
            <p className="text-gray-600">Manage your earnings and withdrawals</p>
          </div>
        </div>
        <button
          onClick={loadWalletData}
          className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </button>
      </div>

      {/* Currency Selector */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium text-gray-900">Primary Currency</h3>
            <p className="text-xs text-gray-500">Choose your preferred currency for wallet display</p>
          </div>
          <div className="flex items-center space-x-2">
            {['USD', 'AED', 'EUR', 'GBP'].map((currency) => (
              <button
                key={currency}
                onClick={() => handleCurrencyChange(currency)}
                className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  selectedCurrency === currency
                    ? 'bg-teal-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                disabled={loading}
              >
                {currency}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        {/* Total Balance */}
        <div className="bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-teal-100 text-sm">Total Balance</p>
              <div className="flex items-center space-x-2">
                {showBalance ? (
                  <h3 className="text-2xl font-bold">
                    {formatCurrency(wallet?.totalBalance || 0, wallet?.primaryCurrency)}
                  </h3>
                ) : (
                  <h3 className="text-2xl font-bold">••••••</h3>
                )}
                <button
                  onClick={() => setShowBalance(!showBalance)}
                  className="text-teal-100 hover:text-white"
                >
                  {showBalance ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>
            <DollarSign className="w-8 h-8 text-teal-200" />
          </div>
        </div>

        {/* This Month Earned */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">This Month Earned</p>
              <h3 className="text-xl font-bold text-gray-900">
                {formatCurrency(statistics?.period?.totalEarned || 0, wallet?.primaryCurrency)}
              </h3>
            </div>
            <TrendingUp className="w-6 h-6 text-green-500" />
          </div>
        </div>

        {/* Total Withdrawn */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm">Total Withdrawn</p>
              <h3 className="text-xl font-bold text-gray-900">
                {formatCurrency(statistics?.overall?.totalWithdrawn || 0, wallet?.primaryCurrency)}
              </h3>
            </div>
            <TrendingDown className="w-6 h-6 text-blue-500" />
          </div>
        </div>

        {/* Total Orders Amount */}
        <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100 text-sm">Total Orders Value</p>
              <h3 className="text-xl font-bold">
                ${totalOrdersAmount.toFixed(2)}
              </h3>
              <p className="text-purple-200 text-xs mt-1">
                {orders.length} orders | {orders.filter(o => o.status === 'pending').length} pending
              </p>
              <p className="text-purple-200 text-xs">
                Potential wallet credit: ${orders.filter(o => o.status === 'pending').reduce((sum, order) => {
                  const amount = order.orderDetails?.productPrice || 0;
                  const platformFee = amount * 0.1; // 10% platform fee
                  return sum + (amount - platformFee);
                }, 0).toFixed(2)}
              </p>
            </div>
            <Package className="w-6 h-6 text-purple-200" />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-4 mb-8">
        <button
          onClick={() => setShowWithdrawModal(true)}
          className="flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
        >
          <Download className="w-4 h-4 mr-2" />
          Withdraw Money
        </button>
        {/* <button className="flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
          <Settings className="w-4 h-4 mr-2" />
          Wallet Settings
        </button> */}

        <button
          onClick={loadOrdersData}
          disabled={ordersLoading}
          className="flex items-center px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${ordersLoading ? 'animate-spin' : ''}`} />
          {ordersLoading ? 'Loading...' : 'Refresh Orders'}
        </button>

        <button
          onClick={async () => {
            console.log('🔄 Manual wallet refresh...');
            await loadWalletData();
            toast.success('Wallet data refreshed!');
          }}
          disabled={loading}
          className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          {loading ? 'Refreshing...' : 'Refresh Wallet'}
        </button>
        <button
          onClick={async () => {
            const pendingOrders = orders.filter(order =>
              order.status === 'pending' || order.payment?.status === 'pending'
            );

            if (pendingOrders.length === 0) {
              toast.info('No pending orders to complete');
              return;
            }

            const confirmed = window.confirm(
              `Complete payments for ${pendingOrders.length} pending orders? This will credit your wallet.`
            );

            if (confirmed) {
              for (const order of pendingOrders) {
                await handleCompleteOrderPayment(order);
                // Small delay between requests
                await new Promise(resolve => setTimeout(resolve, 500));
              }
            }
          }}
          disabled={orders.filter(o => o.status === 'pending').length === 0}
          className="flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <DollarSign className="w-4 h-4 mr-2" />
          Complete All Pending ({orders.filter(o => o.status === 'pending').length})
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview' },
            { id: 'transactions', label: 'Transactions' },
            { id: 'statistics', label: 'Statistics' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-teal-500 text-teal-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Currency Balances */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Currency Balances</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(wallet?.balances || {}).map(([currency, balance]) => (
                <div key={currency} className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-600">{currency}</p>
                  <p className="text-lg font-semibold text-gray-900">
                    {formatCurrency(balance, currency)}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Transactions */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Recent Transactions</h3>
              <button
                onClick={() => setActiveTab('transactions')}
                className="text-teal-600 hover:text-teal-700 text-sm font-medium"
              >
                View All
              </button>
            </div>
            <div className="space-y-3">
              {transactions && transactions.length > 0 ? (
                transactions.slice(0, 3).map((transaction) => (
                  <div key={transaction._id || transaction.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <span className="text-lg">{getTransactionIcon(transaction.type)}</span>
                      <div>
                        <p className="font-medium text-gray-900">{formatTransactionType(transaction.type)}</p>
                        <p className="text-sm text-gray-600">{transaction.description || 'Transaction'}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-semibold ${getTransactionColor(transaction.type)}`}>
                        {transaction.type === 'credit' ? '+' : '-'}
                        {formatCurrency(transaction.amount, transaction.currency || 'AED')}
                      </p>
                      <p className="text-xs text-gray-500">{formatDate(transaction.createdAt)}</p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">No recent transactions found</p>
                  <p className="text-sm text-gray-400 mt-1">Transactions will appear here after you make sales</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'transactions' && (
        <div className="space-y-6">
          {/* Wallet Transactions */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Wallet Transactions</h3>
            <div className="space-y-3">
              {transactions.length > 0 ? transactions.map((transaction) => (
                <div key={transaction._id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <span className="text-2xl">{getTransactionIcon(transaction.type)}</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{formatTransactionType(transaction.type)}</p>
                      <p className="text-sm text-gray-600">{transaction.description}</p>
                      <p className="text-xs text-gray-500">ID: {transaction.transactionId}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`text-lg font-semibold ${getTransactionColor(transaction.type)}`}>
                      {transaction.type === 'credit' ? '+' : '-'}
                      {formatCurrency(transaction.amount, transaction.currency)}
                    </p>
                    <p className="text-sm text-gray-600">
                      Balance: {formatCurrency(transaction.balanceAfter, transaction.currency)}
                    </p>
                    <p className="text-xs text-gray-500">{new Date(transaction.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>
              )) : (
                <p className="text-gray-500 text-center py-4">No wallet transactions found</p>
              )}
            </div>
          </div>

          {/* Orders Summary */}
          {orders.length > 0 && (
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Orders Summary</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">{orders.length}</p>
                  <p className="text-sm text-gray-600">Total Orders</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">
                    {orders.filter(order => order.status === 'completed').length}
                  </p>
                  <p className="text-sm text-gray-600">Completed</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-yellow-600">
                    {orders.filter(order => order.status === 'pending').length}
                  </p>
                  <p className="text-sm text-gray-600">Pending</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">
                    {orders.filter(order => order.type === 'escrow').length}
                  </p>
                  <p className="text-sm text-gray-600">Escrow Orders</p>
                </div>
              </div>
            </div>
          )}

          {/* Orders List */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Orders History</h3>
              <div className="text-sm text-gray-600">
                <div className="text-right">
                  <div>Total: <span className="font-semibold text-purple-600">${totalOrdersAmount.toFixed(2)}</span></div>
                  <div className="text-xs text-gray-500">
                    Orders in state: {orders.length} | Loading: {ordersLoading ? 'Yes' : 'No'}
                  </div>
                  {ordersPagination && (
                    <div className="text-xs text-gray-500">
                      {ordersPagination.totalOrders} orders total | Page {ordersPagination.currentPage} of {ordersPagination.totalPages}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="space-y-3">
              {/* Debug Info */}
              {/* {process.env.NODE_ENV === 'development' && (
                <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    <strong>Debug:</strong> Orders array length: {orders.length} |
                    Total amount: ${totalOrdersAmount} |
                    Loading: {ordersLoading ? 'Yes' : 'No'}
                  </p>
                  {orders.length > 0 && (
                    <details className="mt-2">
                      <summary className="text-xs text-yellow-700 cursor-pointer">Show raw orders data</summary>
                      <pre className="text-xs mt-2 bg-yellow-100 p-2 rounded overflow-auto max-h-40">
                        {JSON.stringify(orders, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              )} */}

              {orders.length > 0 ? orders.map((order) => (
                <div key={order._id} className="flex items-center justify-between p-4 border border-purple-200 rounded-lg bg-purple-50">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      {order.product?.product_photos?.[0] ? (
                        <img
                          src={`http://localhost:5000/${order.product.product_photos[0]}`}
                          alt={order.product.title}
                          className="w-12 h-12 object-cover rounded-lg"
                        />
                      ) : (
                        <Package className="w-12 h-12 text-purple-600 p-2 bg-purple-100 rounded-lg" />
                      )}
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium text-gray-900">
                          Order #{order.orderNumber?.split('_')[2] || order._id?.slice(-6)}
                        </p>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          order.type === 'escrow'
                            ? 'bg-purple-100 text-purple-700'
                            : 'bg-blue-100 text-blue-700'
                        }`}>
                          {order.type}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 font-medium">
                        {order.product?.title || 'Product Order'}
                      </p>
                      <p className="text-xs text-gray-500">
                        Brand: {order.product?.brand} | Size: {order.product?.size} | {order.product?.condition}
                      </p>
                      <p className="text-xs text-gray-500">
                        Buyer: {order.buyer?.email}
                      </p>
                      <div className="flex items-center space-x-2 text-xs">
                        <span className={`px-2 py-1 rounded-full ${getPaymentMethodColor(order.payment?.method)}`}>
                          {order.payment?.method}
                        </span>
                        <span className={`font-medium ${getOrderStatusColor(order.status)}`}>
                          {order.status}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">
                        Shipping to: {order.shipping?.toAddress?.city}, {order.shipping?.toAddress?.state}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-semibold text-purple-600">
                      ${(order.orderDetails?.productPrice || order.payment?.fees?.total || 0).toFixed(2)}
                    </p>
                    <p className="text-xs text-gray-500">
                      {order.orderDetails?.currency || 'USD'}
                    </p>
                    <p className="text-xs text-gray-500">
                      Qty: {order.orderDetails?.quantity || 1}
                    </p>
                    <p className="text-xs text-gray-500">
                      {new Date(order.createdAt).toLocaleDateString()}
                    </p>

                    {/* Action Buttons */}
                    <div className="mt-2 space-y-1">
                      {/* Complete Payment Button for Pending Orders */}
                      {(order.status === 'pending' || order.payment?.status === 'pending') && !order.paymentCompleted && (
                        <button
                          onClick={() => handleCompleteOrderPayment(order)}
                          className="w-full px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700 transition-colors"
                          title="Complete payment and credit wallet"
                        >
                          Complete Payment
                        </button>
                      )}

                      {/* Payment Completed Message */}
                      {order.paymentCompleted && (
                        <div className="w-full px-3 py-1 bg-gray-100 text-gray-600 text-xs rounded text-center">
                          Payment Completed
                        </div>
                      )}

                      {/* Rating Button for Completed Orders */}
                      {order.status === 'completed' && (
                        <button
                          onClick={() => {
                            setSelectedOrderForRating(order);
                            setShowRatingModal(true);
                          }}
                          className="w-full px-3 py-1 bg-yellow-600 text-white text-xs rounded hover:bg-yellow-700 transition-colors"
                          title="Rate this transaction"
                        >
                          ⭐ Rate Transaction
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              )) : (
                <p className="text-gray-500 text-center py-4">No orders found</p>
              )}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'statistics' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Overall Statistics</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Total Transactions:</span>
                <span className="font-semibold">{statistics?.overall?.totalTransactions || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total Earned:</span>
                <span className="font-semibold text-green-600">
                  {formatCurrency(statistics?.overall?.totalEarned || 0, wallet?.primaryCurrency)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total Withdrawn:</span>
                <span className="font-semibold text-blue-600">
                  {formatCurrency(statistics?.overall?.totalWithdrawn || 0, wallet?.primaryCurrency)}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">This Month</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Transactions:</span>
                <span className="font-semibold">{statistics?.period?.totalTransactions || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Earned:</span>
                <span className="font-semibold text-green-600">
                  {formatCurrency(statistics?.period?.totalEarned || 0, wallet?.primaryCurrency)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Spent:</span>
                <span className="font-semibold text-red-600">
                  {formatCurrency(statistics?.period?.totalSpent || 0, wallet?.primaryCurrency)}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Withdrawal Modal */}
      {showWithdrawModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Withdraw Money</h3>
            <form onSubmit={handleWithdraw}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Amount</label>
                  <input
                    type="number"
                    step="0.01"
                    min="1"
                    value={withdrawalData.amount}
                    onChange={(e) => setWithdrawalData({...withdrawalData, amount: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-teal-500 focus:border-teal-500"
                    placeholder="Enter amount"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Currency</label>
                  <select
                    value={withdrawalData.currency}
                    onChange={(e) => setWithdrawalData({...withdrawalData, currency: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-teal-500 focus:border-teal-500"
                  >
                    <option value="USD">USD</option>
                    <option value="AED">AED</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Withdrawal Method</label>
                  <select
                    value={withdrawalData.withdrawalMethod}
                    onChange={(e) => setWithdrawalData({...withdrawalData, withdrawalMethod: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-teal-500 focus:border-teal-500"
                  >
                    <option value="bank_transfer">Bank Transfer</option>
                    <option value="paypal">PayPal</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Description (Optional)</label>
                  <input
                    type="text"
                    value={withdrawalData.description}
                    onChange={(e) => setWithdrawalData({...withdrawalData, description: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-teal-500 focus:border-teal-500"
                    placeholder="Withdrawal reason"
                  />
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowWithdrawModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700"
                >
                  Withdraw
                </button>
              </div>
            </form>
          </div>
        </div>
      )}





      {/* Rating Modal for Orders */}
      {showRatingModal && selectedOrderForRating && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold">Rate Your Transaction</h3>
              <button
                onClick={() => {
                  setShowRatingModal(false);
                  setSelectedOrderForRating(null);
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>

            <RatingPrompt
              transaction={selectedOrderForRating}
              onRatingSubmitted={(ratingData) => {
                console.log('✅ Rating submitted from wallet:', ratingData);
                toast.success('Thank you for your rating!');
                setShowRatingModal(false);
                setSelectedOrderForRating(null);
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Wallet;
