import React, { useState } from 'react';
import { X, Star, User, Package } from 'lucide-react';
import { toast } from 'react-toastify';
import StarRating from './StarRating';
import { submitRating, validateRatingData } from '../../api/RatingService';

const RatingModal = ({ 
  isOpen, 
  onClose, 
  transaction, 
  ratingType, 
  ratedUser,
  onRatingSubmitted = null 
}) => {
  const [rating, setRating] = useState(0);
  const [review, setReview] = useState('');
  const [categories, setCategories] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
console.log(transaction,"transactionjjjjjjjjjjjjj")
  const baseURL = import.meta.env.VITE_API_BASE_URL?.replace(/\/api$/, '') || '';

  if (!isOpen || !transaction) return null;

  const isBuyerRating = ratingType === 'buyer_to_seller';
  const ratingTitle = isBuyerRating ? 'Rate Your Seller' : 'Rate Your Buyer';
  
  // Category options based on rating type
  const categoryOptions = isBuyerRating ? [
    { key: 'communication', label: 'Communication', description: 'How well did they communicate?' },
    { key: 'itemDescription', label: 'Item as Described', description: 'Did the item match the description?' },
    { key: 'shipping', label: 'Shipping Speed', description: 'How quickly did they ship?' }
  ] : [
    { key: 'payment', label: 'Payment', description: 'How quickly did they pay?' },
    { key: 'buyerCommunication', label: 'Communication', description: 'How well did they communicate?' }
  ];

  const handleCategoryRating = (categoryKey, categoryRating) => {
    setCategories(prev => ({
      ...prev,
      [categoryKey]: categoryRating
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (rating === 0) {
      toast.error('Please select a rating');
      return;
    }

    const ratingData = {
      rating,
      review: review.trim(),
      categories,
      ratingType
    };
    // Validate rating data
    const validation = validateRatingData(ratingData);
    if (!validation.isValid) {
      const firstError = Object.values(validation.errors)[0];
      toast.error(firstError);
      return;
    }

    try {
      setIsSubmitting(true);
      
      await submitRating(transaction._id, ratingData,transaction.type);
      
      toast.success('Rating submitted successfully!');
      
      // Call callback if provided
      if (onRatingSubmitted) {
        onRatingSubmitted(ratingData);
      }
      
      // Reset form
      setRating(0);
      setReview('');
      setCategories({});
      
      onClose();
      
    } catch (error) {
      console.error('Error submitting rating:', error);
      const errorMessage = error.response?.data?.error || 'Failed to submit rating';
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setRating(0);
      setReview('');
      setCategories({});
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="relative bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">{ratingTitle}</h2>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* User Info */}
          <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
              {ratedUser?.profile ? (
                <img
                  src={`${baseURL}${ratedUser.profile}`}
                  alt={`${ratedUser.firstName} ${ratedUser.lastName}`}
                  className="w-full h-full object-cover"
                />
              ) : (
                <User className="w-6 h-6 text-gray-400" />
              )}
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-gray-900">
                {ratedUser?.firstName} {ratedUser?.lastName}
              </h3>
              <p className="text-sm text-gray-600">
                {isBuyerRating ? 'Seller' : 'Buyer'}
              </p>
            </div>
          </div>

          {/* Product Info */}
          <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
              {transaction.product?.product_photos?.[0] ? (
                <img
                  src={`${baseURL}${transaction.product.product_photos[0]}`}
                  alt={transaction.product.title}
                  className="w-full h-full object-cover"
                />
              ) : (
                <Package className="w-6 h-6 text-gray-400" />
              )}
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-gray-900 text-sm">
                {transaction.product?.title || 'Product'}
              </h4>
              <p className="text-xs text-gray-600">
                Transaction: {transaction.transactionId}
              </p>
            </div>
          </div>

          {/* Overall Rating */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700">
              Overall Rating *
            </label>
            <div className="flex items-center space-x-3">
              <StarRating
                rating={rating}
                interactive={true}
                onRatingChange={setRating}
                size="lg"
              />
              <span className="text-sm text-gray-600">
                {rating > 0 ? `${rating} out of 5` : 'Select rating'}
              </span>
            </div>
          </div>

          {/* Category Ratings */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-700">Rate specific aspects:</h4>
            {categoryOptions.map((category) => (
              <div key={category.key} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-700">
                      {category.label}
                    </label>
                    <p className="text-xs text-gray-500">{category.description}</p>
                  </div>
                  <StarRating
                    rating={categories[category.key] || 0}
                    interactive={true}
                    onRatingChange={(value) => handleCategoryRating(category.key, value)}
                    size="sm"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Review Text */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Write a review (optional)
            </label>
            <textarea
              value={review}
              onChange={(e) => setReview(e.target.value)}
              placeholder={`Share your experience with ${ratedUser?.firstName}...`}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 resize-none"
              rows={4}
              maxLength={1000}
              disabled={isSubmitting}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Help others by sharing your experience</span>
              <span>{review.length}/1000</span>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || rating === 0}
              className="flex-1 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {isSubmitting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Submitting...
                </>
              ) : (
                'Submit Rating'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RatingModal;
