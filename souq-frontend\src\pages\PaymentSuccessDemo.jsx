import React from 'react';
import { useNavigate } from 'react-router-dom';
import { CheckCircle, Package, Shield, Home, Star, Eye, CreditCard } from 'lucide-react';

const PaymentSuccessDemo = () => {
  const navigate = useNavigate();

  // Mock transaction data for demonstration
  const standardTransaction = {
    id: "STD-175163254684-NT1CGUFO0",
    amount: 21.54,
    currency: "USD",
    status: "payment_processing",
    paymentGateway: "Stripe",
    paymentMethod: "credit_card",
    paymentType: "standard",
    product: {
      title: "Men Regular Fit Self Design Spread Collar Casual Shirt white",
      price: 12.00,
      product_photos: ["/api/uploads/products/shirt.jpg"],
      brand: "Fashion Brand",
      condition: "new"
    },
    createdAt: new Date().toISOString()
  };

  const escrowTransaction = {
    id: "ESC-175163254684-NT1CGUFO0",
    amount: 21.54,
    currency: "USD",
    status: "payment_processing",
    paymentGateway: "Stripe",
    paymentMethod: "credit_card",
    paymentType: "escrow",
    fees: {
      platformFee: 2.15,
      gatewayFee: 0.64,
      totalFees: 2.79
    },
    product: {
      title: "Men Regular Fit Self Design Spread Collar Casual Shirt white",
      price: 12.00,
      product_photos: ["/api/uploads/products/shirt.jpg"],
      brand: "Fashion Brand",
      condition: "new"
    },
    escrowTransaction: {
      _id: "escrow_transaction_id_123"
    },
    createdAt: new Date().toISOString()
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'completed':
      case 'funds_held':
        return 'bg-green-100 text-green-800';
      case 'processing':
      case 'payment_processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending_payment':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const PaymentSuccessCard = ({ transaction, title }) => (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
      {/* Success Header */}
      <div className="bg-gradient-to-r from-teal-500 to-teal-600 px-6 py-8 text-center">
        <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="w-12 h-12 text-teal-600" />
        </div>
        <h1 className="text-3xl font-bold text-white mb-2">Payment Successful!</h1>
        <p className="text-teal-100 text-lg">
          Your payment has been processed successfully
        </p>
      </div>

      {/* Transaction Details */}
      <div className="p-6">
        <div className="mb-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-2">{title}</h2>
        </div>
        
        <div className="space-y-6">
          {/* Transaction Info */}
          <div className="border-b border-gray-200 pb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Transaction Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Transaction ID</p>
                <p className="font-medium text-gray-900 text-sm break-all">{transaction.id}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Amount</p>
                <p className="font-medium text-gray-900">
                  {transaction.currency} {transaction.amount?.toFixed(2)}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Payment Method</p>
                <p className="font-medium text-gray-900 capitalize">
                  {transaction.paymentGateway} Credit Card
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Status</p>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(transaction.status)}`}>
                  {transaction.status === 'payment_processing' ? 'payment processing' : transaction.status}
                </span>
              </div>
              {transaction.fees && (
                <>
                  <div>
                    <p className="text-sm text-gray-500">Platform Fee</p>
                    <p className="font-medium text-gray-900">
                      {transaction.currency} {transaction.fees.platformFee?.toFixed(2)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Gateway Fee</p>
                    <p className="font-medium text-gray-900">
                      {transaction.currency} {transaction.fees.gatewayFee?.toFixed(2)}
                    </p>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Product Info */}
          <div className="border-b border-gray-200 pb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Details</h3>
            <div className="flex items-start space-x-4">
              <div className="w-20 h-20 bg-gray-200 rounded-lg border border-gray-200 flex items-center justify-center">
                <Package className="w-8 h-8 text-gray-400" />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 text-lg mb-1">{transaction.product.title}</h4>
                <p className="text-sm text-gray-600 mb-2">
                  Price: <span className="font-medium text-gray-900">{transaction.currency} {transaction.product.price?.toFixed(2)}</span>
                </p>
                <p className="text-sm text-gray-500">
                  Brand: <span className="text-gray-700">{transaction.product.brand}</span>
                </p>
                <p className="text-sm text-gray-500">
                  Condition: <span className="text-gray-700 capitalize">{transaction.product.condition}</span>
                </p>
              </div>
            </div>
          </div>

          {/* Payment Type Info */}
          {transaction.paymentType === 'escrow' ? (
            <div className="bg-teal-50 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Shield className="w-6 h-6 text-teal-600 mt-0.5" />
                <div>
                  <h3 className="font-medium text-teal-900">Escrow Protection Active</h3>
                  <p className="text-sm text-teal-700 mt-1">
                    Your payment is being processed and will be securely held in escrow once completed.
                    You will be notified when the payment is confirmed and the seller can ship your order.
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <CreditCard className="w-6 h-6 text-blue-600 mt-0.5" />
                <div>
                  <h3 className="font-medium text-blue-900">Payment Processing</h3>
                  <p className="text-sm text-blue-700 mt-1">
                    Your payment is being processed. You will be notified once the payment is confirmed
                    and the seller can ship your order.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Next Steps */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-3">What happens next?</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-teal-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                  1
                </div>
                <p className="text-sm text-gray-700">Seller prepares and ships your item</p>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-xs font-bold">
                  2
                </div>
                <p className="text-sm text-gray-700">You receive tracking information</p>
              </div>
              {transaction.paymentType === 'escrow' && (
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-xs font-bold">
                    3
                  </div>
                  <p className="text-sm text-gray-700">Confirm delivery to release payment to seller</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="bg-gray-50 px-6 py-4">
        <div className="flex flex-col sm:flex-row gap-3">
          <button className="flex-1 bg-teal-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-teal-700 transition-colors flex items-center justify-center gap-2">
            <Package className="w-5 h-5" />
            Products
          </button>
          
          <button className="flex-1 bg-yellow-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-yellow-700 transition-colors flex items-center justify-center gap-2">
            <Star className="w-5 h-5" />
            Rate Transaction
          </button>
        </div>
        
        {transaction.paymentType === 'escrow' && (
          <div className="mt-3">
            <button className="w-full bg-white text-teal-600 border border-teal-600 py-3 px-4 rounded-lg font-medium hover:bg-teal-50 transition-colors flex items-center justify-center gap-2">
              <Eye className="w-5 h-5" />
              View Transaction Details
            </button>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Payment Success UI Demo</h1>
          <p className="text-gray-600">Examples of standard and escrow payment success pages</p>
          <button
            onClick={() => navigate('/')}
            className="mt-4 bg-gray-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors flex items-center justify-center gap-2 mx-auto"
          >
            <Home className="w-5 h-5" />
            Back to Home
          </button>
        </div>

        <PaymentSuccessCard 
          transaction={standardTransaction} 
          title="Standard Payment Success"
        />
        
        <PaymentSuccessCard 
          transaction={escrowTransaction} 
          title="Escrow Payment Success"
        />
      </div>
    </div>
  );
};

export default PaymentSuccessDemo;
