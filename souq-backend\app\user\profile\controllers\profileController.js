const { updateProfileRequestDTO, updateProfileResponseDTO, getProfileResponseDTO, getAnotherUserProfileRes } = require('../dto/user.dto')
const User = require('../../../../db/models/userModel')
const { successResponse, errorResponse } = require('../../../../utils/responseHandler');


exports.getProfile = async (req, res) => {
  try {

    const response = getProfileResponseDTO(req.user);
    // console.log(req.user);
    return res.status(200).json(response);
  } catch (err) {
    return res.status(500).json({
      success: false,
      message: "Failed to fetch profile",
      error: err.message
    });
  }
};


exports.updateProfile = async (req, res) => {
  try {
    const userId = req.user.id;

    const { userName } = req.body;
    if (userName) {
      const userExists = await User.findOne({ userName, id: { $ne: userId } });
      if (userExists) {
        return res.status(400).json({
          success: false,
          message: 'Username already taken',
        });
      }
    }

    const updateData = updateProfileRequestDTO(req.body);

    const updatedUser = await User.findOneAndUpdate(
      { id: userId },
      updateData,
      { new: true, runValidators: true }
    ).select('-password -otp');

    if (!updatedUser) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }
    return res.status(200).json(updateProfileResponseDTO(updatedUser));

  } catch (err) {
    return res.status(500).json({
      success: false,
      message: 'Failed to update profile',
      error: err.message,
    });
  }
};

exports.deleteUser = async (req, res) => {
  try {
    const userId = req.user.id;

    const deletedUser = await User.findOneAndUpdate(
      { id: userId, deletedAt: null },
      { deletedAt: new Date() },
      { new: true }
    );

    if (!deletedUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found or already deleted',
      });
    }

    return res.status(200).json({
      success: true,
      message: 'User soft deleted successfully',
    });

  } catch (err) {
    return res.status(500).json({
      success: false,
      message: 'Failed to delete user',
      error: err.message,
    });
  }
};


exports.uploadProfile = async (req, res) => {
  try {
    const userId = req.user.id;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded',
      });
    }

    const updatedUser = await User.findOneAndUpdate(
      { id: userId },
      { profile: `/uploads/profile/${req.file.filename}` },
      { new: true }
    ).select('-password -otp');

    return res.status(200).json({
      success: true,
      message: 'Profile photo updated successfully',
      data: {
        profile: updatedUser.profile
      }
    });

  } catch (err) {
    return res.status(500).json({
      success: false,
      message: 'Failed to upload profile photo',
      error: err.message
    });
  }
};

exports.getAnotherUserProfile = async (req, res) => {
  try {
    const { userId } = req.params;

    console.log('🔍 getAnotherUserProfile - Requested userId:', userId);
    console.log('🔍 getAnotherUserProfile - userId type:', typeof userId);
    console.log('🔍 getAnotherUserProfile - userId length:', userId.length);

    // Validate ObjectId format
    const mongoose = require('mongoose');
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      console.log('❌ Invalid ObjectId format:', userId);
      return res.status(400).json({
        success: false,
        message: 'Invalid user ID format',
      });
    }

    const user = await User.findOne({ _id: userId, deletedAt: null }).select('-password -otp');
    console.log('🔍 getAnotherUserProfile - User found:', !!user);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    let isFollowingUser = false;

    if (req.user) {
      const me = await User.findById(req.user._id).select('following');
      isFollowingUser = me.following.some(
        (followedUserId) => followedUserId.toString() === user._id.toString()
      );
    }

    const response = getAnotherUserProfileRes(user, isFollowingUser);

    return res.status(200).json(response);

  } catch (err) {
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch user profile',
      error: err.message,
    });
  }
};



exports.followUser = async (req, res) => {
  try {
    const loggedInUserId = req.user._id;
    const userToFollowId = req.params.id;

    if (loggedInUserId.toString() === userToFollowId) {
      return errorResponse(res, "You cannot follow yourself", 400);
    }

    const userToFollow = await User.findById(userToFollowId);
    const loggedInUser = await User.findById(loggedInUserId);

    if (!userToFollow) {
      return errorResponse(res, "User to follow not found", 404);
    }

    // Check if already following
    if (userToFollow.followers.includes(loggedInUserId)) {
      return errorResponse(res, "You already follow this user", 400);
    }

    // Add logged-in user to followers of target user
    userToFollow.followers.push(loggedInUserId);
    await userToFollow.save();

    // Add target user to following of logged-in user
    loggedInUser.following.push(userToFollowId);
    await loggedInUser.save();

    return successResponse(res, "User followed successfully");
  } catch (error) {
    return errorResponse(res, "Failed to follow user", 500, error.message);
  }
};

exports.unfollowUser = async (req, res) => {
  try {
    const loggedInUserId = req.user._id;
    const userToUnfollowId = req.params.id;

    if (loggedInUserId.toString() === userToUnfollowId) {
      return errorResponse(res, "You cannot unfollow yourself", 400);
    }

    const userToUnfollow = await User.findById(userToUnfollowId);
    const loggedInUser = await User.findById(loggedInUserId);

    if (!userToUnfollow) {
      return errorResponse(res, "User to unfollow not found", 404);
    }

    // Check if not following already
    if (!userToUnfollow.followers.includes(loggedInUserId)) {
      return errorResponse(res, "You do not follow this user", 400);
    }

    // Remove logged-in user from followers of target user
    userToUnfollow.followers = userToUnfollow.followers.filter(
      (id) => id.toString() !== loggedInUserId.toString()
    );
    await userToUnfollow.save();

    // Remove target user from following of logged-in user
    loggedInUser.following = loggedInUser.following.filter(
      (id) => id.toString() !== userToUnfollowId.toString()
    );
    await loggedInUser.save();

    return successResponse(res, "User unfollowed successfully");
  } catch (error) {
    return errorResponse(res, "Failed to unfollow user", 500, error.message);
  }
};

exports.getFollowers = async (req, res) => {
  try {
    const userId = req.params.id;
    const loggedInUserId = req.user ? req.user._id.toString() : null;

    // Get the target user's followers
    const user = await User.findById(userId).populate({
      path: 'followers',
      select: 'userName firstName lastName profile lastLoginAt',
    });

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    let myFollowingIds = [];

    // If you're logged in, get who you follow
    if (loggedInUserId) {
      const me = await User.findById(loggedInUserId).select('following');
      myFollowingIds = me.following.map(f => f.toString()); // convert to string array for easy check
    }

    // Now for each follower of the target user, check if YOU follow them
    const followers = user.followers.map(follower => ({
      id: follower._id,
      name: `${follower.firstName} ${follower.lastName}`,
      userName: follower.userName,
      profile: follower.profile || null,
      lastLoginAt: follower.lastLoginAt || null,
      isFollowedByMe: myFollowingIds.includes(follower._id.toString()) // ✅ core logic here
    }));

    return res.status(200).json({
      success: true,
      message: 'Followers fetched successfully',
      data: { followers }
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch followers',
      error: error.message,
    });
  }
};

exports.getFollowing = async (req, res) => {
  try {
    const userId = req.params.id;
    const loggedInUserId = req.user ? req.user._id.toString() : null;

    // Find the user and populate their following list
    const user = await User.findById(userId).populate({
      path: 'following',
      select: 'userName firstName lastName profile lastLoginAt',
    });

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    let myFollowingIds = [];

    // If logged in, get who I (token user) follow
    if (loggedInUserId) {
      const me = await User.findById(loggedInUserId).select('following');
      myFollowingIds = me.following.map(f => f.toString());
    }

    // For each followee, check if I also follow them
    const following = user.following.map(followee => ({
      id: followee._id,
      name: `${followee.firstName} ${followee.lastName}`,
      userName: followee.userName,
      profile: followee.profile || null,
      lastLoginAt: followee.lastLoginAt || null,
      isFollowedByMe: myFollowingIds.includes(followee._id.toString())
    }));

    return res.status(200).json({
      success: true,
      message: 'Following fetched successfully',
      data: { following }
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch following',
      error: error.message,
    });
  }
};

