import React, { useState, useEffect } from "react";
import { FaStar, FaBox } from "react-icons/fa";
import { getUserRatings } from "../../api/RatingService";
import { getProfile } from "../../api/AuthService";
import LoadingSpinner from "../common/LoadingSpinner";
import { formatDistanceToNow } from 'date-fns';

const ReviewSection = () => {
  const [filter, setFilter] = useState("all");
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [userProfile, setUserProfile] = useState(null);
  const [averageRating, setAverageRating] = useState(0);
  const [totalRatings, setTotalRatings] = useState(0);

  const baseURL = import.meta.env.VITE_API_BASE_URL?.replace(/\/api$/, '') || '';

  useEffect(() => {
    fetchUserProfileAndRatings();
  }, []);

  const fetchUserProfileAndRatings = async () => {
    try {
      setLoading(true);

      // Get current user profile
      const profileResponse = await getProfile();
      if (profileResponse?.success) {
        const userData = profileResponse.data.data;
        setUserProfile(userData);

        console.log('👤 Fetching ratings for user:', userData._id);

        // Fetch ratings for this user (ratings received by this user)
        const ratingsResponse = await getUserRatings(userData._id, {
          type: 'received',
          page: 1,
          limit: 50
        });

        console.log('📊 Ratings response:', ratingsResponse);

        if (ratingsResponse?.success) {
          const ratingsData = ratingsResponse.data?.ratings || [];
          const avgRating = ratingsResponse.data?.averageRating || {};

          console.log('📋 Raw ratings data:', ratingsData);
          console.log('⭐ Average rating data:', avgRating);

          // Transform ratings data to match UI structure
          const transformedReviews = ratingsData.map(rating => {
            const reviewerName = `${rating.ratedBy?.firstName || ''} ${rating.ratedBy?.lastName || ''}`.trim();
            const productImage = rating.product?.product_photos?.[0];

            return {
              id: rating._id,
              user: reviewerName || 'Anonymous',
              avatar: rating.ratedBy?.profile
                ? `${baseURL}${rating.ratedBy.profile}`
                : `https://ui-avatars.com/api/?name=${rating.ratedBy?.firstName?.[0] || 'U'}`,
              rating: rating.rating,
              message: rating.review || (rating.rating === 5 ? 'Auto-feedback: Sale completed successfully' : 'No review message'),
              timeAgo: formatDistanceToNow(new Date(rating.createdAt), { addSuffix: true }),
              type: rating.review && rating.review.trim() ? "member" : "auto",
              product: {
                title: rating.product?.title || 'Product',
                image: productImage
                  ? (productImage.startsWith('http') ? productImage : `${baseURL}/${productImage}`)
                  : null,
                id: rating.product?._id
              },
              ratingType: rating.ratingType,
              categories: rating.categories || {}
            };
          });

          console.log('✨ Transformed reviews:', transformedReviews);

          setReviews(transformedReviews);
          setAverageRating(avgRating.averageRating || 0);
          setTotalRatings(avgRating.totalRatings || transformedReviews.length);
        } else {
          console.warn('⚠️ Failed to fetch ratings:', ratingsResponse);
        }
      } else {
        console.warn('⚠️ Failed to fetch user profile:', profileResponse);
      }
    } catch (error) {
      console.error('❌ Error fetching ratings:', error);
      // Set empty state on error
      setReviews([]);
      setAverageRating(0);
      setTotalRatings(0);
    } finally {
      setLoading(false);
    }
  };

  const filteredReviews = reviews.filter((r) => {
    if (filter === "member") return r.type === "member";
    if (filter === "auto") return r.type === "auto";
    return true;
  });

  const memberReviewsCount = reviews.filter(r => r.type === "member").length;
  const autoReviewsCount = reviews.filter(r => r.type === "auto").length;

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="">
      <div className="flex flex-col items-start">
        {/* Rating and stars in same row */}
        <div className="flex items-center space-x-2">
          <h2 className="text-6xl text-gray-800">
            {averageRating > 0 ? averageRating.toFixed(1) : '0.0'}
          </h2>
          <div className="flex text-yellow-400">
            {[...Array(5)].map((_, index) => (
              <FaStar
                key={index}
                className={`text-md ${
                  index < Math.floor(averageRating)
                    ? 'text-yellow-500'
                    : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <p className="text-md text-gray">({totalRatings})</p>
        </div>
      </div>

      {/* Filter and Summary Section */}
      <div className="flex flex-wrap gap-4 items-center mb-6 justify-between mt-4">
        <div className="flex gap-6 text-md text-gray-600">
          <div className="flex items-center gap-2">
            <FaStar className="text-yellow-500 text-md" />
            <span>Member reviews ({memberReviewsCount})</span>
          </div>
          <div className="flex items-center gap-2">
            <FaStar className="text-yellow-500 text-md" />
            <span>Automatic reviews ({autoReviewsCount})</span>
          </div>
        </div>

        {/* Filter Buttons */}
        <div className="flex gap-2 text-md font-medium">
          <button
            className={`px-4 py-1 rounded-full border ${filter === "all" ? "bg-teal-600 text-white" : "bg-white"}`}
            onClick={() => setFilter("all")}
          >
            All
          </button>
          <button
            className={`px-4 py-1 rounded-full border ${filter === "member" ? "bg-teal-600 text-white" : "bg-white"}`}
            onClick={() => setFilter("member")}
          >
            From members
          </button>
          <button
            className={`px-4 py-1 rounded-full border ${filter === "auto" ? "bg-teal-600 text-white" : "bg-white"}`}
            onClick={() => setFilter("auto")}
          >
            Automatic
          </button>
        </div>
      </div>

      {/* Review List */}
      <div className="space-y-6">
        {filteredReviews.length === 0 ? (
          <div className="text-center py-12">
            <FaBox className="mx-auto text-gray-400 text-4xl mb-4" />
            <p className="text-gray-500 text-lg">No reviews yet</p>
            <p className="text-gray-400 text-sm">Reviews from buyers will appear here</p>
          </div>
        ) : (
          filteredReviews.map((review, index) => (
            <div key={review.id} className={`bg-white rounded-lg border p-4 ${index !== filteredReviews.length - 1 ? "mb-4" : ""}`}>
              {/* Product Information */}
              {review.product && (
                <div className="flex items-center gap-3 mb-3 p-2 bg-gray-50 rounded-lg">
                  {review.product.image ? (
                    <img
                      src={review.product.image}
                      alt={review.product.title}
                      className="w-12 h-12 object-cover rounded-lg"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                      <FaBox className="text-gray-400" />
                    </div>
                  )}
                  <div className="flex-1">
                    <p className="font-medium text-sm text-gray-800">{review.product.title}</p>
                    <p className="text-xs text-gray-500">
                      {review.ratingType === 'buyer_to_seller' ? 'Buyer review' : 'Seller review'}
                    </p>
                  </div>
                </div>
              )}

              {/* Review Content */}
              <div className="flex gap-4 items-start">
                <img
                  src={review.avatar}
                  alt={review.user}
                  className="w-12 h-12 rounded-full object-cover"
                  onError={(e) => {
                    e.target.src = `https://ui-avatars.com/api/?name=${review.user?.[0] || 'U'}&background=e5e7eb&color=6b7280`;
                  }}
                />
                <div className="flex-grow">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-semibold text-sm text-gray-800">
                      {review.user || 'Anonymous'}
                    </span>
                    <span className="text-xs text-gray-500">{review.timeAgo}</span>
                  </div>

                  {/* Rating Stars */}
                  <div className="flex items-center mb-2">
                    {[...Array(5)].map((_, i) => (
                      <FaStar
                        key={i}
                        className={`text-sm ${
                          i < review.rating ? 'text-yellow-500' : 'text-gray-300'
                        }`}
                      />
                    ))}
                    <span className="ml-2 text-sm text-gray-600">({review.rating}/5)</span>
                  </div>

                  {/* Review Message */}
                  <p className="text-sm text-gray-700 leading-relaxed">
                    {review.message}
                  </p>

                  {/* Category Ratings (if available) */}
                  {review.categories && Object.keys(review.categories).length > 0 && (
                    <div className="mt-3 pt-3 border-t border-gray-100">
                      <p className="text-xs text-gray-500 mb-2">Detailed ratings:</p>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        {Object.entries(review.categories).map(([category, rating]) => (
                          <div key={category} className="flex justify-between">
                            <span className="text-gray-600 capitalize">
                              {category.replace(/([A-Z])/g, ' $1').trim()}:
                            </span>
                            <div className="flex">
                              {[...Array(5)].map((_, i) => (
                                <FaStar
                                  key={i}
                                  className={`text-xs ${
                                    i < rating ? 'text-yellow-500' : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default ReviewSection;
