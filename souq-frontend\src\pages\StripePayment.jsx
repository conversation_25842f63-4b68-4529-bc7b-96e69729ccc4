import React, { useState, useEffect, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { toast } from 'react-toastify';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { ArrowLeft, CreditCard, Shield, Lock } from 'lucide-react';
import { isPaymentGatewayEnabled, areApiCallsPrevented } from '../config/paymentConfig';
import { processStripePaymentWithCardData, formatStripeError } from '../utils/stripeHelpers';
import usePaymentSecurity from '../hooks/usePaymentSecurity';

const CheckoutForm = ({ clientSecret, transactionId, cardData, autoProcess, paymentType }) => {
  const stripe = useStripe();
  const elements = useElements();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(autoProcess);
  const [error, setError] = useState(null);
  const [processed, setProcessed] = useState(false);

  // Auto-processing disabled - user will enter card details manually
  React.useEffect(() => {
    console.log('🔍 StripePayment - Manual payment mode');
    console.log('Card data auto-processing disabled - user will enter details manually');
  }, [stripe]);

  const processPaymentWithCardData = async () => {
    console.log('🚀 processPaymentWithCardData called');

    if (processed) {
      console.log('⚠️ Payment already processed, skipping');
      return;
    }

    setProcessed(true);
    setLoading(true);
    setError(null);

    try {
      console.log('🔄 Processing payment with provided card data...');
      console.log('Card data details:', {
        hasNumber: !!cardData?.number,
        hasExpiry: !!(cardData?.expMonth && cardData?.expYear),
        hasCvc: !!cardData?.cvc,
        hasName: !!cardData?.name,
        clientSecret: clientSecret?.substring(0, 20) + '...'
      });
      console.log('Card data received:', {
        hasNumber: !!cardData?.number,
        hasExpiry: !!(cardData?.expMonth && cardData?.expYear),
        hasCvc: !!cardData?.cvc,
        hasName: !!cardData?.name
      });

      // Use helper function to process payment
      const result = await processStripePaymentWithCardData(stripe, clientSecret, cardData);

      if (result.success) {
        console.log('✅ Payment succeeded:', result.paymentIntent);
        toast.success('Payment successful!');

        // Navigate to appropriate success page
        const successUrl = paymentType === 'escrow'
          ? `/escrow/payment-success?transaction=${transactionId}`
          : `/payment-success?transaction=${transactionId}&type=standard`;

        navigate(successUrl);
      } else {
        const errorMessage = formatStripeError(result.error);
        console.error('❌ Payment failed:', result.error);
        setError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (err) {
      console.error('❌ Payment processing error:', err);
      const errorMessage = formatStripeError(err.message);
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setLoading(true);
    setError(null);

    const card = elements.getElement(CardElement);

    try {
      const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: card,
        }
      });

      if (error) {
        console.error('Payment failed:', error);
        setError(error.message);
        toast.error(error.message);
      } else {
        console.log('Payment succeeded:', paymentIntent);
        toast.success('Payment successful!');

        // Navigate to appropriate success page
        const successUrl = paymentType === 'escrow'
          ? `/escrow/payment-success?transaction=${transactionId}`
          : `/payment-success?transaction=${transactionId}&type=standard`;

        navigate(successUrl);
      }
    } catch (err) {
      console.error('Payment error:', err);
      setError('An unexpected error occurred. Please try again.');
      toast.error('Payment failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
        fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
        fontSmoothing: 'antialiased',
      },
      invalid: {
        color: '#9e2146',
      },
    },
    hidePostalCode: false,
  };

  // Always show manual payment form - auto-processing disabled

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Card Element */}
      <div className="bg-white border border-gray-300 rounded-lg p-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <CreditCard className="w-4 h-4 inline mr-2" />
          Card Information
        </label>
        <CardElement options={cardElementOptions} />
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Security Notice */}
      <div className="bg-teal-50 border border-teal-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Shield className="w-5 h-5 text-teal-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-teal-900">Secure Payment</h3>
            <p className="text-sm text-teal-700 mt-1">
              Your payment is protected by Stripe's industry-leading security and your funds are held in escrow until delivery is confirmed.
            </p>
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={!stripe || loading}
        className={`w-full py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2 ${
          loading || !stripe
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-teal-600 text-white hover:bg-teal-700'
        }`}
      >
        {loading ? (
          <>
            <LoadingSpinner size={20} />
            Processing Payment...
          </>
        ) : (
          <>
            <Lock className="w-5 h-5" />
            Complete Payment
          </>
        )}
      </button>
    </form>
  );
};

const StripePayment = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 🔒 Security: Only prevent access if user has completed payment and is trying to return
  const { isPaymentRecentlyCompleted } = usePaymentSecurity();

  // Check if user should be blocked from accessing this page
  useEffect(() => {
    if (isPaymentRecentlyCompleted()) {
      toast.warning('⚠️ For security reasons, you cannot return to payment pages after completing a transaction');
      navigate('/payment-security-warning', { replace: true });
    }
  }, [navigate, isPaymentRecentlyCompleted]);

  const {
    clientSecret,
    publishableKey,
    transactionId,
    cardData,
    autoProcess = false,
    paymentType = 'escrow'
  } = location.state || {};

  // Debug logging
  React.useEffect(() => {
    console.log('🔍 StripePayment - Received state:', {
      hasClientSecret: !!clientSecret,
      hasPublishableKey: !!publishableKey,
      transactionId,
      hasCardData: !!cardData,
      autoProcess,
      paymentType,
      cardDataDetails: cardData ? {
        hasNumber: !!cardData.number,
        hasExpiry: !!(cardData.expMonth && cardData.expYear),
        hasCvc: !!cardData.cvc,
        hasName: !!cardData.name
      } : null
    });
  }, []);

  // Memoize the Stripe promise to prevent continuous API calls
  const stripePromise = useMemo(() => {
    console.log('🔍 Creating Stripe promise:', {
      hasPublishableKey: !!publishableKey,
      publishableKey: publishableKey?.substring(0, 12) + '...',
      apiCallsPrevented: areApiCallsPrevented('stripe')
    });

    if (!publishableKey) {
      console.error('❌ No publishable key provided');
      return null;
    }

    // Check if Stripe API calls are prevented
    if (areApiCallsPrevented('stripe')) {
      console.warn('⚠️ Stripe API calls are prevented by configuration');
      return null;
    }

    console.log('🔄 Loading Stripe with publishable key:', publishableKey.substring(0, 12) + '...');
    return loadStripe(publishableKey);
  }, [publishableKey]);

  useEffect(() => {
    let isMounted = true; // Flag to prevent state updates if component unmounts

    if (!clientSecret || !publishableKey || !transactionId) {
      if (isMounted) {
        setError('Missing payment information. Please try again.');
        setLoading(false);
      }
      return;
    }

    // Initialize Stripe
    const initializeStripe = async () => {
      try {
        console.log('🔄 Initializing Stripe...', { hasStripePromise: !!stripePromise, isMounted });

        if (stripePromise && isMounted) {
          const stripeInstance = await stripePromise; // Wait for Stripe to load
          console.log('✅ Stripe loaded successfully:', { hasStripeInstance: !!stripeInstance });

          if (isMounted) {
            setLoading(false);
          }
        } else {
          console.log('⚠️ Stripe promise not available or component unmounted');
        }
      } catch (err) {
        console.error('❌ Failed to load Stripe:', err);
        if (isMounted) {
          setError('Failed to load payment system. Please try again.');
          setLoading(false);
        }
      }
    };

    initializeStripe();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [clientSecret, publishableKey, transactionId, stripePromise]);

  const handleGoBack = () => {
    navigate(-1);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size={60} fullScreen />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full mx-auto p-6">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CreditCard className="w-8 h-8 text-red-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Payment Error</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={handleGoBack}
              className="w-full bg-teal-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-teal-700 transition-colors flex items-center justify-center gap-2"
            >
              <ArrowLeft className="w-5 h-5" />
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={handleGoBack}
            className="flex items-center text-teal-600 hover:text-teal-700 mb-4"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Checkout
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Complete Your Payment</h1>
          <p className="text-gray-600 mt-2">
            Enter your payment details to complete your secure purchase
          </p>
        </div>

        {/* Payment Form */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          {stripePromise && (
            <Elements stripe={stripePromise}>
              <CheckoutForm
                clientSecret={clientSecret}
                transactionId={transactionId}
                cardData={cardData}
                autoProcess={autoProcess}
                paymentType={paymentType}
              />
            </Elements>
          )}
        </div>

        {/* Footer */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            Powered by Stripe • Your payment information is secure and encrypted
          </p>
        </div>
      </div>
    </div>
  );
};

export default StripePayment;
